{"version": 3, "sources": ["uni-app:///main.js", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/pages/me/me.vue?ef0e", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/pages/me/me.vue?2dd1", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/pages/me/me.vue?79a9", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/pages/me/me.vue?23ac", "uni-app:///pages/me/me.vue", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/pages/me/me.vue?5ab6", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/pages/me/me.vue?7d70"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "user", "token", "asset", "onLoad", "onShow", "methods", "contact", "console"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,WAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+G;AAC/G;AACsD;AACL;AACc;;;AAG/D;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,wEAAM;AACR,EAAE,6EAAM;AACR,EAAE,sFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sKAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAA4lB,CAAgB,snBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACUhnB;;;;;;;;;;eAGA;EACAC;IACA;MACAC;MACA;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;MACA;MACA;MACA;IACA;MACA;IACA;EACA;EACAC;IACA;IACA;IACA;MACA;MACA;MACA;IACA;MACA;MACA;MACA;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAC;MACAC;IACA;EAGA;AACA;AAAA,2B;;;;;;;;;;;;;AC5DA;AAAA;AAAA;AAAA;AAAuoC,CAAgB,6mCAAG,EAAC,C;;;;;;;;;;;ACA3pC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/me/me.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/me/me.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./me.vue?vue&type=template&id=259fb574&\"\nvar renderjs\nimport script from \"./me.vue?vue&type=script&lang=js&\"\nexport * from \"./me.vue?vue&type=script&lang=js&\"\nimport style0 from \"./me.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/me/me.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./me.vue?vue&type=template&id=259fb574&\"", "var components\ntry {\n  components = {\n    newme: function () {\n      return import(\n        /* webpackChunkName: \"components/newme/newme\" */ \"@/components/newme/newme.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./me.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./me.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<!-- <view class=\"page1\" v-if=\"public.isnews==0\">\r\n\t\t<oldme :user='user'></oldme>\r\n\t</view> -->\r\n\t<view class=\"page\">\r\n\t\t<newme ref='newme' :user='user' :asset=\"asset\"></newme>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\tuserInfo\r\n\t} from \"@/api/public.js\"\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tuser: uni.getStorageSync('user'),\r\n\t\t\t\t// public: uni.getStorageSync(\"public\"),\r\n\t\t\t\ttoken: uni.getStorageSync(\"TOKEN\"),\r\n\t\t\t\tasset: uni.getStorageSync('ASSET') || {}\r\n\t\t\t};\r\n\t\t},\r\n\r\n\t\tonLoad() {\r\n\t\t\tlet token = uni.getStorageSync(\"TOKEN\")\r\n\t\t\tif (token) {\r\n\t\t\t\tthis.user = uni.getStorageSync('user')\r\n\t\t\t\tthis.token = uni.getStorageSync(\"TOKEN\")\r\n\t\t\t\t// this.getUser()\r\n\t\t\t} else {\r\n\t\t\t\tthis.user = null\r\n\t\t\t}\r\n\t\t},\r\n\t\tonShow() {\r\n\t\t\tlet token = uni.getStorageSync(\"TOKEN\")\r\n\t\t\tthis.asset = uni.getStorageSync(\"ASSET\") || {}\r\n\t\t\tif (token) {\r\n\t\t\t\tthis.user = uni.getStorageSync('user')\r\n\t\t\t\tthis.token = uni.getStorageSync(\"TOKEN\")\r\n\t\t\t\t// this.getUser()\r\n\t\t\t} else {\r\n\t\t\t\tthis.$refs.newme.percentage = 0\r\n\t\t\t\tthis.$refs.newme.token = null\r\n\t\t\t\tthis.user = null\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// async getUser() {\r\n\t\t\t// \tlet user = await userInfo()\r\n\t\t\t// \tif (user.code == 1) {\r\n\t\t\t// \t\tuni.setStorageSync('user', user.data)\r\n\t\t\t// \t\tthis.$refs.newme.getUser()\r\n\t\t\t// \t}\r\n\t\t\t// },\r\n\t\t\tcontact(e) {\r\n\t\t\t\tconsole.log(e);\r\n\t\t\t},\r\n\r\n\r\n\t\t}\r\n\t}\r\n</script>\r\n<style lang=\"scss\">\r\n\tpage {\r\n\t\tmin-height: 100vh;\r\n\t\tbackground-color: #F7F7F7 !important;\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./me.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./me.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754557252868\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}