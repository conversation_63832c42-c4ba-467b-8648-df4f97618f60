{"version": 3, "sources": ["uni-app:///main.js", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/subpkg/report_all/upload_content/upload_content.vue?9b7b", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/subpkg/report_all/upload_content/upload_content.vue?95d0", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/subpkg/report_all/upload_content/upload_content.vue?4db5", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/subpkg/report_all/upload_content/upload_content.vue?c064", "uni-app:///subpkg/report_all/upload_content/upload_content.vue", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/subpkg/report_all/upload_content/upload_content.vue?2810", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/subpkg/report_all/upload_content/upload_content.vue?34cd"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "feedbackContent", "fileList", "taskMaterialId", "taskId", "feedbackImage", "feedbackAttachment", "methods", "submit", "errCode", "msg", "uni", "title", "icon", "urlToFile", "fetch", "response", "blob", "type", "afterRead", "that", "lists", "fileListLen", "item", "status", "message", "console", "i", "bold", "result", "tmpSecretId", "tmpSecret<PERSON>ey", "sessionToken", "startTime", "expiredTime", "bucket", "region", "key", "cos", "SecretId", "<PERSON><PERSON><PERSON>", "SecurityToken", "StartTime", "ExpiredTime", "Bucket", "Region", "Key", "Body", "onProgress", "url", "reject", "resolve", "setTimeout", "deletePic", "onLoad"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,uBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuI;AACvI;AACkE;AACL;AACsC;;;AAGnG;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,oFAAM;AACR,EAAE,qGAAM;AACR,EAAE,8GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAwmB,CAAgB,koBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;ACgB5nB;AAEA;AAGA;AAAA;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACA;kBACA;gBACA;gBAAA;gBAAA;gBAAA,OAMA;kBACAP;kBACAE;kBACAC;kBACAC;kBACAC;gBACA;cAAA;gBAAA;gBATAN;gBACAS;gBACAC;gBAQA;kBACAC;oBACAC;oBACAC;kBACA;kBACAF;gBACA;kBACAA;oBACAC;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAIA;IACAC;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAEAC;cAAA;gBAAAC;gBAAA;gBAAA,OACAA;cAAA;gBAAAC;gBAAA,kCAGA;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC,eACA;gBACAC;gBACAC;gBACAD;kBACA,qDACAE;oBACAC;oBACAC;kBAAA,GACA;gBACA;gBACAC;gBACA;gBAAA,8DACAC;kBAAA;kBAAA;oBAAA;sBAAA;wBAAA;0BAAA;0BAAA,OACA;wBAAA;0BAAAC;0BAAA;4BAAA,GACA;8BAAA;gCAAA;gCAAA;kCAAA;oCAAA;sCAAA;wCAAA;wCAAA;wCAAA,OAEA;sCAAA;wCAAAC;wCACA;0CAEA;0CACAH;0CAAA,wDAWAG,cACAA,0BAVAC,iDACAC,mDACAC,mDACAC,6CACAC,iDACAC,uCACAC,uCACAC,iCAKA;0CACA;0CACAC;4CACAC;4CACAC;4CACAC;4CACAC;4CACAC;0CACA,IACA;0CACAL;4CACAM;4CACAC;4CACAC;4CACAC;4CAAA;4CACAC;8CACA;4CAAA;0CAEA;4CACAtB;4CACA;4CACAN;8CACAI;8CACAC;8CACAwB;4CACA;4CACA3B;4CACA;8CACAI;8CACAwB;4CACA;8CACAxB;8CACAyB;4CACA;0CACA;wCACA;wCAAA;wCAAA;sCAAA;wCAAA;wCAAA;wCAGA;wCACAC;0CACA;wCACA;sCAAA;sCAAA;wCAAA;oCAAA;kCAAA;gCAAA;8BAAA,CAGA;8BAAA;gCAAA;8BAAA;4BAAA;0BAAA;wBAAA;wBAAA;0BAAA;sBAAA;oBAAA;kBAAA;gBAAA;gBAlEAzB;cAAA;gBAAA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAAA;gBAAA;gBAAA;cAAA;gBAoEA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA0B;MACA;IACA;EACA;EACAC;IACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACtLA;AAAA;AAAA;AAAA;AAA2qC,CAAgB,ipCAAG,EAAC,C;;;;;;;;;;;ACA/rC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "subpkg/report_all/upload_content/upload_content.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './subpkg/report_all/upload_content/upload_content.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./upload_content.vue?vue&type=template&id=5a243c4a&scoped=true&\"\nvar renderjs\nimport script from \"./upload_content.vue?vue&type=script&lang=js&\"\nexport * from \"./upload_content.vue?vue&type=script&lang=js&\"\nimport style0 from \"./upload_content.vue?vue&type=style&index=0&id=5a243c4a&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"5a243c4a\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"subpkg/report_all/upload_content/upload_content.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./upload_content.vue?vue&type=template&id=5a243c4a&scoped=true&\"", "var components\ntry {\n  components = {\n    uUpload: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-upload/u-upload\" */ \"uview-ui/components/u-upload/u-upload.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./upload_content.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./upload_content.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container\">\r\n\t\t<view class=\"value-container\">\r\n\t\t\t<textarea maxlength='100' v-model=\"feedbackContent\" class=\"value-input\" placeholder=\"请输入文本内容\" />\r\n\t\t</view>\r\n\t\t<view class=\"image-content\">\r\n\t\t\t<u-upload height=\"200rpx\" width=\"200rpx\" uploadText=\"上传照片\" :fileList=\"fileList\" @afterRead=\"afterRead\"\r\n\t\t\t\t@delete=\"deletePic\" name=\"1\" multiple :maxCount=\"6\"></u-upload>\r\n\t\t\t<!-- <image src=\"@/static/imgs/camera.png\" mode=\"\"></image>\r\n\t\t\t\t<view class=\"img\">上传图片</view> -->\r\n\t\t</view>\r\n\t\t<view class=\"btn\" @click=\"submit\">提交</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport COS from 'cos-js-sdk-v5';\r\n\r\n\timport {\r\n\t\tgetCosStsAuth,\r\n\t\tsubTaskDone\r\n\t} from '@/api/user.js'\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tfeedbackContent: '',\r\n\t\t\t\tfileList: [],\r\n\t\t\t\ttaskMaterialId: '',\r\n\t\t\t\ttaskId: '',\r\n\t\t\t\tfeedbackImage: [],\r\n\t\t\t\tfeedbackAttachment: []\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 点击提交\r\n\t\t\tasync submit() {\r\n\t\t\t\tthis.feedbackImage = []\r\n\t\t\t\tthis.fileList.forEach(item => {\r\n\t\t\t\t\tthis.feedbackImage.push(item.url)\r\n\t\t\t\t})\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst {\r\n\t\t\t\t\t\tdata,\r\n\t\t\t\t\t\terrCode,\r\n\t\t\t\t\t\tmsg\r\n\t\t\t\t\t} = await subTaskDone({\r\n\t\t\t\t\t\tfeedbackContent: this.feedbackContent,\r\n\t\t\t\t\t\ttaskMaterialId: this.taskMaterialId ? this.taskMaterialId : '',\r\n\t\t\t\t\t\ttaskId: this.taskId,\r\n\t\t\t\t\t\tfeedbackImage: this.feedbackImage,\r\n\t\t\t\t\t\tfeedbackAttachment: [],\r\n\t\t\t\t\t})\r\n\t\t\t\t\tif (errCode == 0) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '提交成功',\r\n\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tuni.navigateBack()\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: msg,\r\n\t\t\t\t\t\t\ticon: 'error'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (e) {\r\n\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tasync urlToFile(url, fileName, mimeType) {\r\n\t\t\t\t// 使用 fetch 获取图片的二进制数据\r\n\t\t\t\tconst response = await fetch(url);\r\n\t\t\t\tconst blob = await response.blob(); // 转换为 Blob 对象\r\n\r\n\t\t\t\t// 将 Blob 转换为 File 对象\r\n\t\t\t\treturn new File([blob], fileName, {\r\n\t\t\t\t\ttype: mimeType\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t// // 使用示例\r\n\t\t\t// const imageUrl = 'https://example.com/path/to/image.jpg';\r\n\t\t\t// urlToFile(imageUrl, 'image.jpg', 'image/jpeg')\r\n\t\t\t// .then((file) => {\r\n\t\t\t// \tconsole.log('文件对象', file);\r\n\t\t\t// \t// 这里可以调用上传逻辑\r\n\t\t\t// })\r\n\t\t\t// .catch((err) => {\r\n\t\t\t// \tconsole.error('转换失败', err);\r\n\t\t\t// });\r\n\t\t\t//图片上传\r\n\t\t\tasync afterRead(event) {\r\n\t\t\t\tlet that = this\r\n\t\t\t\t// 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式\r\n\t\t\t\tlet lists = [].concat(event.file)\r\n\t\t\t\tlet fileListLen = this.fileList.length\r\n\t\t\t\tlists.map((item) => {\r\n\t\t\t\t\tthis.fileList.push({\r\n\t\t\t\t\t\t...item,\r\n\t\t\t\t\t\tstatus: 'uploading',\r\n\t\t\t\t\t\tmessage: '上传中'\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t\tconsole.log(event)\r\n\t\t\t\tthis.loading = true;\r\n\t\t\t\tfor (let i = 0; i < lists.length; i++) {\r\n\t\t\t\t\tlet bold = await this.urlToFile(lists[i].url, lists[i].name, lists[i].type)\r\n\t\t\t\t\treturn new Promise(async (resolve, reject) => {\r\n\t\t\t\t\t\ttry {\r\n\t\t\t\t\t\t\tconst result = await getCosStsAuth(lists[i].name)\r\n\t\t\t\t\t\t\tif (result.errCode == 0) {\r\n\r\n\t\t\t\t\t\t\t\t// 在返回值里取临时密钥信息，上传的文件路径信息\r\n\t\t\t\t\t\t\t\tconsole.log(result)\r\n\t\t\t\t\t\t\t\tconst {\r\n\t\t\t\t\t\t\t\t\ttmpSecretId,\r\n\t\t\t\t\t\t\t\t\ttmpSecretKey,\r\n\t\t\t\t\t\t\t\t\tsessionToken,\r\n\t\t\t\t\t\t\t\t\tstartTime,\r\n\t\t\t\t\t\t\t\t\texpiredTime,\r\n\t\t\t\t\t\t\t\t\tbucket,\r\n\t\t\t\t\t\t\t\t\tregion,\r\n\t\t\t\t\t\t\t\t\tkey,\r\n\t\t\t\t\t\t\t\t} = {\r\n\t\t\t\t\t\t\t\t\t...result.data,\r\n\t\t\t\t\t\t\t\t\t...result.data.credentials\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t// 创建 JS SDK 实例，传入临时密钥参数\r\n\t\t\t\t\t\t\t\t// 其他配置项可参考下方 初始化配置项\r\n\t\t\t\t\t\t\t\tconst cos = new COS({\r\n\t\t\t\t\t\t\t\t\tSecretId: tmpSecretId,\r\n\t\t\t\t\t\t\t\t\tSecretKey: tmpSecretKey,\r\n\t\t\t\t\t\t\t\t\tSecurityToken: sessionToken,\r\n\t\t\t\t\t\t\t\t\tStartTime: startTime,\r\n\t\t\t\t\t\t\t\t\tExpiredTime: expiredTime\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t// 上传文件\r\n\t\t\t\t\t\t\t\tcos.uploadFile({\r\n\t\t\t\t\t\t\t\t\tBucket: bucket,\r\n\t\t\t\t\t\t\t\t\tRegion: region,\r\n\t\t\t\t\t\t\t\t\tKey: key,\r\n\t\t\t\t\t\t\t\t\tBody: bold, // 要上传的文件对象。\r\n\t\t\t\t\t\t\t\t\tonProgress: function(progressData) {\r\n\t\t\t\t\t\t\t\t\t\t// console.log('上传进度：', progressData);\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}, function(err, data) {\r\n\t\t\t\t\t\t\t\t\tconsole.log('data', data)\r\n\t\t\t\t\t\t\t\t\tlet item = that.fileList[fileListLen]\r\n\t\t\t\t\t\t\t\t\tthat.fileList.splice(fileListLen, 1, Object.assign(item, {\r\n\t\t\t\t\t\t\t\t\t\tstatus: 'success',\r\n\t\t\t\t\t\t\t\t\t\tmessage: '',\r\n\t\t\t\t\t\t\t\t\t\turl: data.Location\r\n\t\t\t\t\t\t\t\t\t}))\r\n\t\t\t\t\t\t\t\t\tfileListLen++\r\n\t\t\t\t\t\t\t\t\tif (err) {\r\n\t\t\t\t\t\t\t\t\t\tconsole.log('err')\r\n\t\t\t\t\t\t\t\t\t\treject(err);\r\n\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\tconsole.log('data')\r\n\t\t\t\t\t\t\t\t\t\tresolve(data);\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t} catch (err) {\r\n\t\t\t\t\t\t\t//移除图片\r\n\t\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t\tthis.fileList.splice(fileListLen, 1)\r\n\t\t\t\t\t\t\t}, 1500)\r\n\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\tthis.loading = false\r\n\t\t\t},\r\n\t\t\tdeletePic(event) {\r\n\t\t\t\tthis.fileList.splice(event.index, 1)\r\n\t\t\t},\r\n\t\t},\r\n\t\tonLoad(option) {\r\n\t\t\tthis.taskMaterialId = option.id\r\n\t\t\tthis.taskId = option.taskId\r\n\t\t},\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.container {\r\n\t\tpadding: 30px;\r\n\t\tbox-sizing: border-box;\r\n\r\n\t\t.value-container {\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 241rpx;\r\n\t\t\tbackground: #FFFFFF;\r\n\t\t\tborder-radius: 20rpx;\r\n\t\t\tpadding: 15px;\r\n\t\t\tbox-sizing: border-box;\r\n\t\t\tmargin-bottom: 30px;\r\n\r\n\t\t\t.value-input {\r\n\r\n\t\t\t\tborder: 0;\r\n\t\t\t\tborder-radius: 8rpx;\r\n\t\t\t\toutline: none;\r\n\r\n\t\t\t}\r\n\r\n\t\t\t.value-input:focus {\r\n\t\t\t\tborder-color: #1BB394;\r\n\t\t\t}\r\n\r\n\r\n\t\t\t::v-deep .checkbox__inner {\r\n\t\t\t\tborder-radius: 16rpx !important;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.image-content {\r\n\t\t\twidth: 100%;\r\n\t\t\tpadding: 20rpx;\r\n\t\t\tbackground: #FFFFFF;\r\n\t\t\tborder-radius: 20rpx;\r\n\r\n\t\t\t/deep/.u-upload__wrap__preview {\r\n\t\t\t\twidth: 180rpx !important;\r\n\t\t\t\theight: 180rpx !important;\r\n\t\t\t\tborder-radius: 12rpx !important;\r\n\t\t\t}\r\n\r\n\t\t\t/deep/.u-icon--right,\r\n\t\t\t/deep/.u-upload__button__text {\r\n\t\t\t\tfont-size: 30rpx !important;\r\n\t\t\t\tborder-radius: 12rpx !important;\r\n\t\t\t}\r\n\r\n\t\t\t/deep/.uicon-camera-fill {\r\n\t\t\t\tfont-size: 80rpx !important;\r\n\t\t\t\tline-height: 80rpx !important;\r\n\t\t\t}\r\n\r\n\r\n\r\n\t\t}\r\n\r\n\t\t.btn {\r\n\t\t\twidth: 469rpx;\r\n\t\t\theight: 80rpx;\r\n\t\t\tline-height: 80rpx;\r\n\t\t\ttext-align: center;\r\n\t\t\tfont-weight: bold;\r\n\t\t\tfont-size: 30rpx;\r\n\t\t\tcolor: #FFFFFF;\r\n\t\t\tposition: absolute;\r\n\t\t\tbottom: 50rpx;\r\n\t\t\tleft: 50%;\r\n\t\t\ttransform: translateX(-50%);\r\n\t\t\tbackground: linear-gradient(268deg, #26C8AC 0%, #19C990 100%);\r\n\t\t\tborder-radius: 40rpx;\r\n\t\t}\r\n\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./upload_content.vue?vue&type=style&index=0&id=5a243c4a&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./upload_content.vue?vue&type=style&index=0&id=5a243c4a&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754557255797\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}