{"version": 3, "sources": ["webpack:///G:/code/uniapp/daxuetonguni/uniapp/components/close/close.vue?37cb", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/components/close/close.vue?7d23", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/components/close/close.vue?f439", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/components/close/close.vue?6cf7", "uni-app:///components/close/close.vue", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/components/close/close.vue?31db", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/components/close/close.vue?50c7"], "names": ["name", "props", "shopping_trolley_list", "seatShow", "type", "addAdish", "default", "isAdd", "showCart", "pageStatus", "data", "show", "value", "trolley_list", "capsule_button", "tableNumber", "specificationShow", "order_id", "order_type", "editList", "cart_id", "list", "count", "totalPrice", "watch", "console", "methods", "addNum", "carList", "specification", "setTimeout", "inputlenght", "reduce", "uni", "item", "add", "valChange", "add_joinCar", "store_id", "goods_id", "spu_id", "spu_info", "order_reduce", "remove", "goback", "title", "icon"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,aAAa,qRAEN;AACP,KAAK;AACL;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,aAAa,yTAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACxDA;AAAA;AAAA;AAAA;AAA+lB,CAAgB,ynBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;AC6EnnB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAMA;EACAA;EACAC;IACAC;IACAC;MACAC;IACA;IACAC;MACAC;IACA;IACAC;MACAH;MACAE;IACA;IACAE;MACAJ;MACAE;IACA;IACAG;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;MACA;QACAC;QACA;QACA;MACA;IACA;IACAlB;MACA;MACAkB;MACA;IACA;IACAvB;MACAuB;MACA;MACA;IACA;IACAjB;MACA;QACA;MACA;IACA;EACA;EACAkB;IACAC;MAAA;MACA;MACA;MACAF;MACA;QACA;MACA;MACA;QACA;QACAA;MACA;IAEA;IAEAG;MACA;MACA;IACA;IACAC;MAAA;MACA;QACA;QACA;QACA;QACA;QACA;UACA;UACA;QACA;UACA;QACA;QACAC;UACA;UACA;QACA;MACA;IAGA;IAEAC;MAAA;MACA;MACA;QACA;UACA;QACA;MACA;IACA;IACAC;MACA;QACAC;QACA;QACA;MACA;MACA;QACA;UAAA;QAAA;QACAA;MAEA;QAEA;UACA;YACAC;UACA;QACA;QACAD;MAEA;MACA;IACA;IACAE;MACA;QACA;UACAD;QACA;MACA;MACAD;MACA;IACA;IACAG;MACAX;IACA;IACAY;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACA;kBACAC;kBACAC;kBACAC;kBACAlB;kBACAmB;gBACA;cAAA;gBANA/B;gBAOA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAgC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACA;kBACAJ;kBACAC;kBACAC;kBACAlB;kBACAmB;kBACArB;gBACA;cAAA;gBAPAV;gBAQA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAiC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACAV;;gBAEA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAIA;IACAW;MACA;QACAX;UACAY;UACAC;QACA;QACA;MACA;MACArB;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAGA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACzUA;AAAA;AAAA;AAAA;AAA0oC,CAAgB,gnCAAG,EAAC,C;;;;;;;;;;;ACA9pC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/close/close.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./close.vue?vue&type=template&id=565f98b6&\"\nvar renderjs\nimport script from \"./close.vue?vue&type=script&lang=js&\"\nexport * from \"./close.vue?vue&type=script&lang=js&\"\nimport style0 from \"./close.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/close/close.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./close.vue?vue&type=template&id=565f98b6&\"", "var components\ntry {\n  components = {\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-popup/u-popup\" */ \"uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uEmpty: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-empty/u-empty\" */ \"uview-ui/components/u-empty/u-empty.vue\"\n      )\n    },\n    uNumberBox: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-number-box/u-number-box\" */ \"uview-ui/components/u-number-box/u-number-box.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.show = true\n    }\n    _vm.e1 = function ($event) {\n      _vm.show = false\n    }\n  }\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./close.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./close.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<!-- 悬浮结算 -->\r\n\t<view>\r\n\t\t<view class=\"box\">\r\n\t\t\t<view class=\"box_content\" @tap=\"show=true\">\r\n\t\t\t\t<view class=\"box_img\">\r\n\t\t\t\t\t<image src=\"@/static/Project_drawing 27.png\" mode=\"\"></image>\r\n\t\t\t\t\t<text>{{count||0}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"box_price\">\r\n\t\t\t\t\t<text>总额</text>\r\n\t\t\t\t\t<text style=\"font-size: 34rpx;\">¥</text><text> {{totalPrice||0}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"box_close\" @tap=\"goback()\">\r\n\t\t\t\t去结算\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<u-popup :show=\"show\" :round=\"10\" zIndex='10071' mode=\"bottom\" @close=\"show=false\">\r\n\t\t\t<view class=\"upcart\">\r\n\t\t\t\t<view class=\"upcart_top\">\r\n\t\t\t\t\t<view class=\"upcart_top_left\">\r\n\t\t\t\t\t\t已选商品\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"upcart_top_right\" @tap=\"remove()\">\r\n\t\t\t\t\t\t<u-icon name=\"trash\" color=\"#676767 \" size=\"30\"></u-icon>\r\n\t\t\t\t\t\t<text>清空</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"upcart_order\">\r\n\t\t\t\t\t<view style=\"margin-top: 150rpx;\" v-if=\"list<1\">\r\n\t\t\t\t\t\t<u-empty mode=\"data\" :iconSize='150' :textSize='24' text='购物车空空如也' icon=\"\"></u-empty>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<scroll-view scroll-y=\"true\" style=\"height: 570rpx;\">\r\n\t\t\t\t\t\t<view class=\"upcart_order_content\" v-for=\"(item,index) in list\" :key=\"item\">\r\n\t\t\t\t\t\t\t<view class=\"upcart_order_content_img\">\r\n\t\t\t\t\t\t\t\t<image :src=\"item.course_cover\" mode=\"\"></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"upcart_order_content_title\">\r\n\t\t\t\t\t\t\t\t<view>{{item.name}}</view>\r\n\t\t\t\t\t\t\t\t<!-- <view class=\"flexc\" style=\"height: 40rpx;\" @click=\"specification(item)\">\r\n\t\t\t\t\t\t\t\t\t<view>{{item.name}}</view>\r\n\t\t\t\t\t\t\t\t\t<view v-if=\" item.name\" class=\"\" style=\"padding: 8rpx 0 0rpx 10rpx;\">\r\n\t\t\t\t\t\t\t\t\t\t<u-icon name='arrow-right' size='24'></u-icon>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view> -->\r\n\t\t\t\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"money\">￥{{item.checkout_price}}</text>\r\n\t\t\t\t\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t\t\t\t\t\t<u-number-box v-model=\"value[index]\" @change=\"valChange\">\r\n\t\t\t\t\t\t\t\t\t\t\t<view slot=\"minus\" class=\"minus\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t@tap=\"reduce(item,index,item.min_quantity)\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<u-icon name=\"minus\" labelSize='20px' color=\"#05B6F6\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tsize=\"24\"></u-icon>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t\t<text slot=\"input\" style=\"width: 50px;text-align: center;\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tclass=\"input\">{{value[index]||item.quantity}}</text>\r\n\t\t\t\t\t\t\t\t\t\t\t<view slot=\"plus\" class=\"plus\" @tap=\"add(item)\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t<u-icon name=\"plus\" labelSize='20px' color=\"#FFFFFF\" size=\"24\"></u-icon>\r\n\t\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t\t</u-number-box>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"\" style=\"height: 80rpx;\"></view>\r\n\t\t\t\t\t</scroll-view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</u-popup>\r\n\r\n\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\torder_joinCar,\r\n\t\torder_reduce,\r\n\t\torder_empty,\r\n\t\tjiac,\r\n\t} from \"@/api/comm.js\"\r\n\texport default {\r\n\t\tname: \"close\",\r\n\t\tprops: {\r\n\t\t\tshopping_trolley_list: {},\r\n\t\t\tseatShow: {\r\n\t\t\t\ttype: Boolean\r\n\t\t\t},\r\n\t\t\taddAdish: {\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\tisAdd: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\tshowCart: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\tpageStatus: {}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tshow: false,\r\n\t\t\t\tvalue: [],\r\n\t\t\t\ttrolley_list: {},\r\n\t\t\t\tcapsule_button: 2,\r\n\t\t\t\ttableNumber: uni.getStorageSync('tableNumber') || false,\r\n\t\t\t\tspecificationShow: false,\r\n\t\t\t\torder_id: \"\",\r\n\t\t\t\torder_type: '',\r\n\t\t\t\teditList: [],\r\n\t\t\t\tcart_id: '',\r\n\t\t\t\tlist: uni.getStorageSync('cartItems') || [],\r\n\t\t\t\tcount: 0,\r\n\t\t\t\ttotalPrice: 0,\r\n\t\t\t};\r\n\t\t},\r\n\r\n\t\twatch: {\r\n\t\t\t'pageStatus.onShow'(value) {\r\n\t\t\t\tif (value) {\r\n\t\t\t\t\tconsole.log('子组件：页面已显示')\r\n\t\t\t\t\tthis.list = uni.getStorageSync('cartItems') || []\r\n\t\t\t\t\tthis.addNum()\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tisAdd: function(newval, oldval) {\r\n\t\t\t\tthis.list = uni.getStorageSync('cartItems') || []\r\n\t\t\t\tconsole.log('list', this.list)\r\n\t\t\t\tthis.addNum()\r\n\t\t\t},\r\n\t\t\tshopping_trolley_list: function(newval, oldval) {\r\n\t\t\t\tconsole.log(newval, oldval, 'watch监听数据变化');\r\n\t\t\t\tthis.trolley_list = newval\r\n\t\t\t\tthis.inputlenght()\r\n\t\t\t},\r\n\t\t\tshowCart(newVal) {\r\n\t\t\t\tif (newVal) {\r\n\t\t\t\t\tthis.show = true;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\taddNum() {\r\n\t\t\t\tthis.count = 0\r\n\t\t\t\tif (!this.list.length) return\r\n\t\t\t\tconsole.log(this.list)\r\n\t\t\t\tthis.totalPrice = this.list.reduce((total, item) => {\r\n\t\t\t\t\treturn total + item.checkout_price * item.quantity;\r\n\t\t\t\t}, 0);\r\n\t\t\t\tthis.list.forEach(item => {\r\n\t\t\t\t\tthis.count += item.quantity\r\n\t\t\t\t\tconsole.log(this.count)\r\n\t\t\t\t})\r\n\r\n\t\t\t},\r\n\r\n\t\t\tcarList() {\r\n\t\t\t\tthis.$emit('update')\r\n\t\t\t\tthis.specificationShow = false\r\n\t\t\t},\r\n\t\t\tspecification(item) {\r\n\t\t\t\tif (item.item_name) {\r\n\t\t\t\t\tthis.show = false\r\n\t\t\t\t\tthis.order_id = item.goods_id\r\n\t\t\t\t\tthis.cart_id = item.id\r\n\t\t\t\t\tthis.order_type = item.type\r\n\t\t\t\t\tif (item.type == 2) {\r\n\t\t\t\t\t\tlet arr = item.item_id.split('_')\r\n\t\t\t\t\t\tthis.editList = arr\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.editList = item.spu_info_ys\r\n\t\t\t\t\t}\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tthis.specificationShow = true\r\n\t\t\t\t\t\tthis.$refs.specificationCard.specification()\r\n\t\t\t\t\t}, 500)\r\n\t\t\t\t}\r\n\r\n\r\n\t\t\t},\r\n\r\n\t\t\tinputlenght() {\r\n\t\t\t\tthis.value = []\r\n\t\t\t\tif (this.trolley_list.data) {\r\n\t\t\t\t\tthis.trolley_list.data.forEach((res, index) => {\r\n\t\t\t\t\t\tthis.value.push(res.count)\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\treduce(val, index) {\r\n\t\t\t\tif (this.count == 1) {\r\n\t\t\t\t\tuni.removeStorageSync('cartItems')\r\n\t\t\t\t\tthis.count = 0\r\n\t\t\t\t\tthis.totalPrice = 0\r\n\t\t\t\t}\r\n\t\t\t\tif (val.quantity == 1) {\r\n\t\t\t\t\tthis.list = this.list.filter(item => item.id != val.id)\r\n\t\t\t\t\tuni.setStorageSync('cartItems', this.list)\r\n\r\n\t\t\t\t} else {\r\n\r\n\t\t\t\t\tthis.list.forEach(item => {\r\n\t\t\t\t\t\tif (item.id == val.id) {\r\n\t\t\t\t\t\t\titem.quantity -= item.min_quantity\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\tuni.setStorageSync('cartItems', this.list)\r\n\r\n\t\t\t\t}\r\n\t\t\t\tthis.addNum()\r\n\t\t\t},\r\n\t\t\tadd(val) {\r\n\t\t\t\tthis.list.forEach(item => {\r\n\t\t\t\t\tif (item.id == val.id) {\r\n\t\t\t\t\t\titem.quantity += item.min_quantity\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\tuni.setStorageSync('cartItems', this.list)\r\n\t\t\t\tthis.addNum()\r\n\t\t\t},\r\n\t\t\tvalChange(e) {\r\n\t\t\t\tconsole.log(e);\r\n\t\t\t},\r\n\t\t\tasync add_joinCar(item, info) {\r\n\t\t\t\tlet data = await order_joinCar({\r\n\t\t\t\t\tstore_id: item.store_id,\r\n\t\t\t\t\tgoods_id: item.goods_id,\r\n\t\t\t\t\tspu_id: item.item_id,\r\n\t\t\t\t\tcount: 1,\r\n\t\t\t\t\tspu_info: info,\r\n\t\t\t\t})\r\n\t\t\t\tthis.$emit('update')\r\n\t\t\t},\r\n\t\t\tasync order_reduce(item, info) {\r\n\t\t\t\tlet data = await order_reduce({\r\n\t\t\t\t\tstore_id: item.store_id,\r\n\t\t\t\t\tgoods_id: item.goods_id,\r\n\t\t\t\t\tspu_id: item.item_id,\r\n\t\t\t\t\tcount: 1,\r\n\t\t\t\t\tspu_info: info,\r\n\t\t\t\t\tcart_id: item.id\r\n\t\t\t\t})\r\n\t\t\t\tthis.$emit('update')\r\n\t\t\t},\r\n\r\n\t\t\t// 清空购物车\r\n\t\t\tasync remove() {\r\n\t\t\t\t// let data = await order_empty()\r\n\t\t\t\t// this.$emit('update')\r\n\t\t\t\tthis.list = []\r\n\t\t\t\tthis.count = 0\r\n\t\t\t\tthis.totalPrice = 0\r\n\t\t\t\tuni.removeStorageSync('cartItems')\r\n\r\n\t\t\t\t// this.show = false\r\n\t\t\t},\r\n\r\n\r\n\r\n\t\t\t// 去结算\r\n\t\t\tgoback() {\r\n\t\t\t\tif (this.count < 1) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请至少选择一个产品',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tconsole.log(this.count)\r\n\t\t\t\tthis.$emit('goPay')\r\n\t\t\t\t// let user = uni.getStorageSync('user')\r\n\t\t\t\t// if (user.order_num == 1) {\r\n\t\t\t\t// uni.showModal({\r\n\t\t\t\t// \ttitle: '提示',\r\n\t\t\t\t// \tcontent: '你有未支付订单,请支付过后重新提交',\r\n\t\t\t\t// \tsuccess: function(res) {\r\n\t\t\t\t// \t\tif (res.confirm) {\r\n\t\t\t\t// \t\t\t//未登录\r\n\t\t\t\t// \t\t\tuni.reLaunch({\r\n\t\t\t\t// \t\t\t\turl: '/pages/order_form/order_form'\r\n\t\t\t\t// \t\t\t})\r\n\t\t\t\t// \t\t} else if (res.cancel) {\r\n\t\t\t\t// \t\t\tconsole.log('用户点击取消');\r\n\t\t\t\t// \t\t}\r\n\t\t\t\t// \t}\r\n\t\t\t\t// });\r\n\t\t\t\t// } else {\r\n\t\t\t\t// if (this.shopping_trolley_list.count > 0) {\r\n\t\t\t\t// \tif (this.shopping_trolley_list.data[0].is_cw == 1) {\r\n\t\t\t\t// \t\tuni.showToast({\r\n\t\t\t\t// \t\t\ttitle: '请至少选择一个商品',\r\n\t\t\t\t// \t\t\ticon: 'none'\r\n\t\t\t\t// \t\t})\r\n\t\t\t\t// \t} else {\r\n\t\t\t\t// \t\tlet url;\r\n\t\t\t\t// \t\tif (this.seatShow) {\r\n\t\t\t\t// \t\t\turl = '/pages/order_all/affirm_order/affirm_order'\r\n\t\t\t\t// \t\t} else {\r\n\t\t\t\t// let url = '/pages/order_all/affirm_order/affirm_order'\r\n\r\n\t\t\t\t// \t\t}\r\n\t\t\t\t// \t\tif (!uni.getStorageSync('userinfo')) {\r\n\t\t\t\t// \t\t\tthis.$emit('register')\r\n\t\t\t\t// \t\t\treturn\r\n\t\t\t\t// \t\t}\r\n\t\t\t\t// \t\tif (this.shopping_trolley_list.count > 0) {\r\n\r\n\t\t\t\t// \t\t} else {\r\n\t\t\t\t// \t\t\tuni.showToast({\r\n\t\t\t\t// \t\t\t\ttitle: '无商品可结算',\r\n\t\t\t\t// \t\t\t\ticon: 'none'\r\n\t\t\t\t// \t\t\t})\r\n\t\t\t\t// \t\t}\r\n\t\t\t\t// \t}\r\n\t\t\t\t// } else {\r\n\t\t\t\t// \tuni.showToast({\r\n\t\t\t\t// \t\ttitle: '请至少选择一个商品',\r\n\t\t\t\t// \t\ticon: 'none'\r\n\t\t\t\t// \t})\r\n\t\t\t\t// }\r\n\r\n\r\n\t\t\t\t// }\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.box {\r\n\t\twidth: 700rpx;\r\n\t\theight: 101rpx;\r\n\t\tbackground: #FFFFFF;\r\n\t\tbox-shadow: 0rpx 3rpx 15rpx 0rpx rgba(191, 202, 211, 0.46);\r\n\t\tborder-radius: 51rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t\tposition: fixed;\r\n\t\tbottom: 20rpx;\r\n\t\tleft: 50%;\r\n\t\ttransform: translateX(-50%);\r\n\t\tz-index: 10072;\r\n\r\n\t\t.box_content {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t}\r\n\r\n\t\t.box_img {\r\n\t\t\twidth: 70rpx;\r\n\t\t\theight: 96rpx;\r\n\t\t\tmargin: 0 30rpx;\r\n\t\t\tmargin-left: 50rpx;\r\n\t\t\tposition: relative;\r\n\r\n\t\t\ttext {\r\n\t\t\t\twidth: 40rpx;\r\n\t\t\t\theight: 40rpx;\r\n\t\t\t\tbackground-color: #00C2A0;\r\n\t\t\t\tborder-radius: 50%;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tline-height: 40rpx;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tright: -20rpx;\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.box_price {\r\n\t\t\ttext:nth-child(1) {\r\n\t\t\t\twidth: 124rpx;\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tfont-weight: 400;\r\n\t\t\t\tcolor: #414141;\r\n\t\t\t\tline-height: 101rpx;\r\n\t\t\t}\r\n\r\n\t\t\ttext:nth-child(2) {\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tfont-weight: 500;\r\n\t\t\t\tcolor: #FB4E44;\r\n\t\t\t\tline-height: 101rpx;\r\n\t\t\t\tmargin-left: 10rpx;\r\n\t\t\t}\r\n\r\n\t\t\ttext:nth-child(3) {\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tfont-weight: 500;\r\n\t\t\t\tcolor: #FB4E44;\r\n\t\t\t\tline-height: 101rpx;\r\n\t\t\t\tmargin-left: 10rpx;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.box_close {\r\n\t\t\twidth: 173rpx;\r\n\t\t\theight: 100%;\r\n\t\t\tbackground: #00C2A0;\r\n\t\t\tborder-radius: 0rpx 89rpx 89rpx 0rpx;\r\n\t\t\tline-height: 101rpx;\r\n\t\t\ttext-align: center;\r\n\t\t\tfont-size: 32rpx;\r\n\t\t\tfont-weight: 600;\r\n\t\t\tcolor: #FDFEFF;\r\n\t\t}\r\n\t}\r\n\r\n\t.upcart {\r\n\t\twidth: 100%;\r\n\t\theight: 700rpx;\r\n\r\n\t\t.upcart_top {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\tpadding: 20rpx 30rpx;\r\n\r\n\t\t\t.upcart_top_left {\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tfont-weight: 400;\r\n\t\t\t\tcolor: #333333;\r\n\t\t\t}\r\n\r\n\t\t\t.upcart_top_right {\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\tfont-weight: 400;\r\n\t\t\t\tcolor: #676767;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.upcart_order_content {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tpadding: 0rpx 20rpx;\r\n\t\t\tbox-sizing: border-box;\r\n\t\t\theight: 135rpx;\r\n\t\t\tmargin-bottom: 30rpx;\r\n\r\n\t\t\t.upcart_order_content_img {\r\n\t\t\t\twidth: 136rpx;\r\n\t\t\t\theight: 135rpx;\r\n\t\t\t\tborder-radius: 7rpx;\r\n\t\t\t\toverflow: hidden;\r\n\t\t\t}\r\n\r\n\t\t\t.upcart_order_content_title {\r\n\t\t\t\twidth: 540rpx;\r\n\t\t\t\tmargin-left: 20rpx;\r\n\r\n\t\t\t\tview:nth-child(1) {\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\tcolor: #353535;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.money {\r\n\t\t\t\t\theight: 45rpx;\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t\tcolor: #E45F3A;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tjustify-content: space-between;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.minus {\r\n\t\twidth: 22px;\r\n\t\theight: 22px;\r\n\t\tborder-width: 1px;\r\n\t\tborder-color: #05B6F6;\r\n\t\tborder-style: solid;\r\n\t\tborder-top-left-radius: 100px;\r\n\t\tborder-top-right-radius: 100px;\r\n\t\tborder-bottom-left-radius: 100px;\r\n\t\tborder-bottom-right-radius: 100px;\r\n\t\t@include flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.input {\r\n\t\t// padding: 0 10px;\r\n\t}\r\n\r\n\t.plus {\r\n\t\twidth: 22px;\r\n\t\theight: 22px;\r\n\t\tbackground-color: #05B6F6;\r\n\t\tborder-radius: 50%;\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\t/* #endif */\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./close.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./close.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754557259012\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}