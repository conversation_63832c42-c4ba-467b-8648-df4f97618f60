{"version": 3, "sources": ["uni-app:///main.js", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/App.vue?8b21", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/App.vue?e058", "uni-app:///App.vue", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/App.vue?3a3c", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/App.vue?213a"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "<PERSON><PERSON>", "prototype", "$way", "fnc", "Object", "keys", "filters", "for<PERSON>ach", "key", "filter", "uni", "http", "tip", "title", "showToast", "duration", "icon", "$x", "share", "require", "mixin", "use", "router", "uView", "$u", "config", "unit", "productionTip", "App", "mpType", "$showMsg", "$utils", "vuePrototype", "k", "isPromise", "obj", "then", "addInterceptor", "returnValue", "res", "Promise", "resolve", "reject", "error", "app", "store", "$mount", "onLaunch", "onLoad", "onShow", "console", "onHide", "methods", "getUser", "loginInfo", "code", "errCode", "msg", "data"], "mappings": ";;;;;;;;;;;;;;;;AAAA;AAE2D;AAE3D;AACA;AACA;AACA;AACA;AACA;AAGA;AAgBA;AAG2B;AAAA;AAAA;AAAA;AA9B3B;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAQ1DC,YAAG,CAACC,SAAS,CAACC,IAAI,GAAGC,iBAAG;AACxB;;AAEAC,MAAM,CAACC,IAAI,CAACC,OAAO,CAAC,CAACC,OAAO,CAAC,UAAAC,GAAG,EAAI;EACnCR,YAAG,CAACS,MAAM,CAACD,GAAG,EAAEF,OAAO,CAACE,GAAG,CAAC,CAAC;AAC9B,CAAC,CAAC;AACFE,GAAG,CAACC,IAAI,GAAGA,gBAAI;AACfD,GAAG,CAACE,GAAG,GAAG,UAACC,KAAK;EAAA,OAAKH,GAAG,CAACI,SAAS,CAAC;IAClCD,KAAK,EAALA,KAAK;IACLE,QAAQ,EAAE,IAAI;IACdC,IAAI,EAAE;EACP,CAAC,CAAC;AAAA;AACF;AACA,IAAMC,EAAE,GAAG,CAAC,CAAC;AACbjB,YAAG,CAACC,SAAS,CAACgB,EAAE,GAAGA,EAAE;AACrB,IAAIC,KAAK,GAAGC,mBAAO,CAAC,wBAAe,CAAC;AACpCnB,YAAG,CAACoB,KAAK,CAACF,KAAK,CAAC;AAKY;;AAE5B;AACAlB,YAAG,CAACqB,GAAG,CAACC,cAAM,CAAC;AACftB,YAAG,CAACqB,GAAG,CAACE,gBAAK,CAAC;AACdb,GAAG,CAACc,EAAE,CAACC,MAAM,CAACC,IAAI,GAAG,KAAK;AAC1B1B,YAAG,CAACyB,MAAM,CAACE,aAAa,GAAG,KAAK;AAChCC,YAAG,CAACC,MAAM,GAAG,KAAK;AAClBnB,GAAG,CAACoB,QAAQ,GAAG,YAA6C;EAAA,IAApCjB,KAAK,uEAAG,SAAS;EAAA,IAAEE,QAAQ,uEAAG,IAAI;EACzDL,GAAG,CAACI,SAAS,CAAC;IACbD,KAAK,EAALA,KAAK;IACLE,QAAQ,EAARA,QAAQ;IACRC,IAAI,EAAE;EACP,CAAC,CAAC;AACH,CAAC;AACD;AACAhB,YAAG,CAACC,SAAS,CAAC8B,MAAM,GAAG,CAAC,CAAC;AACzB3B,MAAM,CAACC,IAAI,CAAC2B,cAAY,CAAC,CAACzB,OAAO,CAAC,UAAA0B,CAAC,EAAI;EACtCjC,YAAG,CAACC,SAAS,CAACgC,CAAC,CAAC,GAAGD,cAAY,CAACC,CAAC,CAAC;AACnC,CAAC,CAAC;AACF,IAAI;EAAA,IACMC,SAAS,GAAlB,SAASA,SAAS,CAACC,GAAG,EAAE;IACvB,OACC,CAAC,CAACA,GAAG,KACJ,sBAAOA,GAAG,MAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,UAAU,CAAC,IACtD,OAAOA,GAAG,CAACC,IAAI,KAAK,UAAU;EAEhC,CAAC,EACD;EACA1B,GAAG,CAAC2B,cAAc,CAAC;IAClBC,WAAW,uBAACC,GAAG,EAAE;MAChB,IAAI,CAACL,SAAS,CAACK,GAAG,CAAC,EAAE;QACpB,OAAOA,GAAG;MACX;MACA,OAAO,IAAIC,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;QACvCH,GAAG,CAACH,IAAI,CAAC,UAACG,GAAG,EAAK;UACjB,IAAIA,GAAG,CAAC,CAAC,CAAC,EAAE;YACXG,MAAM,CAACH,GAAG,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,MAAM;YACNE,OAAO,CAACF,GAAG,CAAC,CAAC,CAAC,CAAC;UAChB;QACD,CAAC,CAAC;MACH,CAAC,CAAC;IACH;EACD,CAAC,CAAC;AACH,CAAC,CAAC,OAAOI,KAAK,EAAE,CAAC;AACjB,IAAMC,GAAG,GAAG,IAAI5C,YAAG,iCACf4B,YAAG;EACNiB,KAAK,EAALA;AAAK,GACJ;AACF,UAAAD,GAAG,EAACE,MAAM,EAAE,C;;;;;;;;;;;;;ACjFZ;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACuD;AACL;AACc;;;AAGhE;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,yEAAM;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAA6lB,CAAgB,unBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACCjnB;AAIA;AAIA;AAEA,eACA;EACAC;IACA;IACA;EACA;EACAC,2BAEA;EACAC;IACAC;EACA;EACAC;IACAD;EACA;EACAE;IACAC;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAEA3C;cAAA;gBAAA4C;gBAAA;gBAAA,OAKA;kBACAC;gBACA;cAAA;gBAAA;gBALAC;gBACAC;gBACAC;gBAKA;kBACAhD;gBAEA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;EAEA;AAEA;AAAA,2B;;;;;;;;;;;;;AC9CA;AAAA;AAAA;AAAA;AAAwoC,CAAgB,8mCAAG,EAAC,C;;;;;;;;;;;ACA5pC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "common/main.js", "sourcesContent": ["import 'uni-pages';\r\n// @ts-ignore\r\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import App from './App'\r\n\r\nimport uView from 'uview-ui'\r\nimport Vue from 'vue'\r\nimport vuePrototype from \"./utils/wxApi.js\"\r\nimport fnc from '@/utils/function.js'\r\nimport http from '@/utils/request.js'\r\nimport store from \"@/store/index.js\"\r\nVue.prototype.$way = fnc\r\n// 过滤器\r\nimport * as filters from './utils/filter.js'\r\nObject.keys(filters).forEach(key => {\r\n\tVue.filter(key, filters[key])\r\n})\r\nuni.http = http\r\nuni.tip = (title) => uni.showToast({\r\n\ttitle,\r\n\tduration: 1500,\r\n\ticon: 'none'\r\n})\r\n// 全局分享\r\nconst $x = {};\r\nVue.prototype.$x = $x;\r\nlet share = require('./utils/share');\r\nVue.mixin(share);\r\n\r\nimport {\r\n\trouter,\r\n\tRouterMount\r\n} from './router/router.js' //路径换成自己的\r\n\r\n// 如此配置即可\r\nVue.use(router)\r\nVue.use(uView)\r\nuni.$u.config.unit = 'rpx'\r\nVue.config.productionTip = false\r\nApp.mpType = 'app'\r\nuni.$showMsg = function(title = '数据加载失败！', duration = 2000) {\r\n\tuni.showToast({\r\n\t\ttitle,\r\n\t\tduration,\r\n\t\ticon: 'none',\r\n\t})\r\n}\r\n// 扩展vue原型方法\r\nVue.prototype.$utils = {}\r\nObject.keys(vuePrototype).forEach(k => {\r\n\tVue.prototype[k] = vuePrototype[k]\r\n})\r\ntry {\r\n\tfunction isPromise(obj) {\r\n\t\treturn (\r\n\t\t\t!!obj &&\r\n\t\t\t(typeof obj === \"object\" || typeof obj === \"function\") &&\r\n\t\t\ttypeof obj.then === \"function\"\r\n\t\t);\r\n\t}\r\n\t// 统一 vue2 API Promise 化返回格式与 vue3 保持一致\r\n\tuni.addInterceptor({\r\n\t\treturnValue(res) {\r\n\t\t\tif (!isPromise(res)) {\r\n\t\t\t\treturn res;\r\n\t\t\t}\r\n\t\t\treturn new Promise((resolve, reject) => {\r\n\t\t\t\tres.then((res) => {\r\n\t\t\t\t\tif (res[0]) {\r\n\t\t\t\t\t\treject(res[0]);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tresolve(res[1]);\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t});\r\n\t\t},\r\n\t});\r\n} catch (error) {}\r\nconst app = new Vue({\r\n\t...App,\r\n\tstore,\r\n})\r\napp.$mount()", "var render, staticRenderFns, recyclableRender, components\nvar renderjs\nimport script from \"./App.vue?vue&type=script&lang=js&\"\nexport * from \"./App.vue?vue&type=script&lang=js&\"\nimport style0 from \"./App.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"App.vue\"\nexport default component.exports", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./App.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./App.vue?vue&type=script&lang=js&\"", "<script>\r\n\timport {\r\n\t\tauto_login,\r\n\t\tget_openid\r\n\t} from '@/api/user.js'\r\n\timport {\r\n\t\tuserInfo,\r\n\t\tcommon\r\n\t} from \"@/api/public.js\"\r\n\timport {\r\n\t\tgetAssets\r\n\t} from \"@/api/comm.js\"\r\n\texport default {\r\n\t\tonLaunch() {\r\n\t\t\tif (uni.getStorageSync('TOKEN')) return\r\n\t\t\tthis.getUser()\r\n\t\t},\r\n\t\tonLoad() {\r\n\r\n\t\t},\r\n\t\tonShow: function() {\r\n\t\t\tconsole.log('App Show')\r\n\t\t},\r\n\t\tonHide: function() {\r\n\t\t\tconsole.log('App Hide')\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tasync getUser() {\r\n\t\t\t\t//获取用户信息\r\n\t\t\t\tconst loginInfo = await uni.login()\r\n\t\t\t\tlet {\r\n\t\t\t\t\terrCode,\r\n\t\t\t\t\tmsg,\r\n\t\t\t\t\tdata\r\n\t\t\t\t} = await auto_login({\r\n\t\t\t\t\tcode: loginInfo.code\r\n\t\t\t\t})\r\n\r\n\t\t\t\tif (errCode == 0) {\r\n\t\t\t\t\tuni.setStorageSync('TOKEN', data.token)\r\n\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t},\r\n\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t@import \"uview-ui/index.scss\";\r\n\r\n\t@import url(\"utils/default.scss\");\r\n\r\n\t::v-deep .u-picker__view__column__item {\r\n\t\tpadding-top: 10rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\timage {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t}\r\n\r\n\tbutton::after {\r\n\t\tborder: none;\r\n\t}\r\n\r\n\tbutton {\r\n\t\tbackground-color: transparent;\r\n\t\tpadding-left: 0;\r\n\t\tpadding-right: 0;\r\n\t\tline-height: inherit;\r\n\t}\r\n\r\n\tbutton {\r\n\t\tborder-radius: 0;\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./App.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./App.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754557260568\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}