{"version": 3, "sources": ["uni-app:///main.js", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/pages/me_all/codeqr/codeqr.vue?a9f4", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/pages/me_all/codeqr/codeqr.vue?a3f7", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/pages/me_all/codeqr/codeqr.vue?06b2", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/pages/me_all/codeqr/codeqr.vue?c228", "uni-app:///pages/me_all/codeqr/codeqr.vue", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/pages/me_all/codeqr/codeqr.vue?3328", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/pages/me_all/codeqr/codeqr.vue?9f61"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "clear1", "clear2", "userall", "time", "showCanvas", "qrCodeUrl", "img", "System_height", "onLoad", "methods", "createQRCode", "qr", "setTimeout", "uni", "canvasId", "success", "fail", "console", "title", "icon", "goback", "delta", "userInfoApi"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACc;;;AAGnE;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,4KAEN;AACP,KAAK;AACL;AACA,aAAa,qRAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtCA;AAAA;AAAA;AAAA;AAAgmB,CAAgB,0nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACwCpnB;AAGA;AAGA;AAIA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AADA;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACA;EACAC;IACA;IACAC;MAAA;MACA;MACA;;MAEA;MACA;QACA;UACA;UACA;;UAEA;UACAC;UACAA;UACAA;UACAA;UACAA;UACAA;;UAEA;UACAA;;UAEA;UACA;;UAEA;UACAA;UACAA;;UAEA;UACAC;YACAC;cACAC;cACAC;gBACA;gBACA;cACA;;cACAC;gBACAC;gBACAJ;kBACAK;kBACAC;gBACA;cACA;YACA;UACA;QACA;UACAF;UACAJ;YACAK;YACAC;UACA;QACA;MACA;IACA;IACA;IACAC;MACAP;QACAQ;MACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBAAAvB;gBACA;kBACAc;kBACA;kBACAI;gBACA;kBACAJ;oBACAK;oBACAC;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA,EACA;IACA;IACA;IACA;IACA;EACA;EACA;EACA;EACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACzKA;AAAA;AAAA;AAAA;AAA2oC,CAAgB,inCAAG,EAAC,C;;;;;;;;;;;ACA/pC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/me_all/codeqr/codeqr.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/me_all/codeqr/codeqr.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./codeqr.vue?vue&type=template&id=72da3553&\"\nvar renderjs\nimport script from \"./codeqr.vue?vue&type=script&lang=js&\"\nexport * from \"./codeqr.vue?vue&type=script&lang=js&\"\nimport style0 from \"./codeqr.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/me_all/codeqr/codeqr.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./codeqr.vue?vue&type=template&id=72da3553&\"", "var components\ntry {\n  components = {\n    height: function () {\n      return import(\n        /* webpackChunkName: \"components/height/height\" */ \"@/components/height/height.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./codeqr.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./codeqr.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<view class=\"back\">\r\n\t\t\t<image src=\"https://yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/preset/dxt_mini/ckg_6.png\"\r\n\t\t\t\tmode=\"\"></image>\r\n\t\t</view>\r\n\t\t<height :hg='System_height'></height>\r\n\t\t<view class=\"nav-tab\">\r\n\t\t\t<view class=\"nav-tab_left\" @tap=\"goback\">\r\n\t\t\t\t<u-icon name=\"arrow-left\" color=\"#FFFFFF\" size=\"32\"></u-icon>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"nav-tab_content\">\r\n\t\t\t\t会员码\r\n\t\t\t</view>\r\n\t\t\t<view class=\"\">\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<view class=\"qr\">\r\n\t\t\t<view class=\"qr_1\" @tap=\"routerTo('/pages/me_all/personage/personage')\">\r\n\t\t\t\t<view class=\"qr_1_img\">\r\n\t\t\t\t\t<image :src=\"userall.avatar\" mode=\"\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<text>{{userall.username}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"qr_2\">\r\n\t\t\t\t<canvas v-if=\"showCanvas\" canvas-id=\"qrcode-canvas\" id=\"qrcode-canvas\"\r\n\t\t\t\t\tstyle=\"width: 300px; height: 300px; position: absolute; left: -9999px\"></canvas>\r\n\t\t\t\t<!-- <image :src=\"img\" mode=\"\"></image> -->\r\n\t\t\t\t<image v-if=\"qrCodeUrl\" :src=\"qrCodeUrl\" mode=\"widthFix\" class=\"ma\" />\r\n\t\t\t</view>\r\n\t\t\t<!-- <view class=\"qr_3\">\r\n\t\t\t\t会员码还有{{time}}秒自动更新，请在店内消费使用\r\n\t\t\t</view> -->\r\n\t\t</view>\r\n\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\tuserInfo\r\n\t} from \"@/api/public.js\"\r\n\timport {\r\n\t\tuser_code\r\n\t} from \"@/api/user.js\"\r\n\timport {\r\n\t\tcellphone\r\n\t} from \"@/utils/type_height.js\"\r\n\t// 引入uqrcode组件\r\n\timport UQRCode from '@/uni_modules/Sansnn-uQRCode/js_sdk/uqrcode/uqrcode.js'\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tclear1: null,\r\n\t\t\t\tclear2: null,\r\n\t\t\t\tuserall: {},\r\n\t\t\t\ttime: 30,\r\n\t\t\t\tshowCanvas: false,\r\n\t\t\t\tqrCodeUrl: '', // 生成的二维码图片路径\r\n\t\t\t\timg: '',\r\n\t\t\t\tSystem_height: cellphone() //系统高度\r\n\t\t\t};\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\t// this.userInfoApi()\r\n\t\t\t// this.user_codeApi()\r\n\t\t\t// // 每30秒刷新一次\r\n\t\t\t// this.clear1 = setInterval(res => {\r\n\t\t\t// \tthis.time--\r\n\t\t\t// \tif (this.time == 0) {\r\n\t\t\t// \t\tthis.time = 30\r\n\t\t\t// \t}\r\n\t\t\t// }, 1000)\r\n\t\t\t// this.clear2 = setInterval(res => {\r\n\t\t\t// \tthis.user_codeApi()\r\n\t\t\t// \tuni.showToast({\r\n\t\t\t// \t\ttitle: '已刷新二维码',\r\n\t\t\t// \t\ticon: \"none\"\r\n\t\t\t// \t})\r\n\t\t\t// }, 30000)\r\n\t\t\tlet id = uni.getStorageSync('user').id\r\n\t\t\tthis.createQRCode(id)\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// / 创建二维码 (修复后的方法)\r\n\t\t\tcreateQRCode(content) {\r\n\t\t\t\t// 显示canvas（微信小程序需要）\r\n\t\t\t\tthis.showCanvas = true\r\n\r\n\t\t\t\t// 等待DOM更新\r\n\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\ttry {\r\n\t\t\t\t\t\t// 创建UQRCode实例\r\n\t\t\t\t\t\tconst qr = new UQRCode()\r\n\r\n\t\t\t\t\t\t// 设置二维码参数\r\n\t\t\t\t\t\tqr.data = content // 使用后端返回的核销码\r\n\t\t\t\t\t\tqr.size = 300 // 二维码大小\r\n\t\t\t\t\t\tqr.margin = 10 // 边距\r\n\t\t\t\t\t\tqr.foregroundColor = '#000000' // 前景色\r\n\t\t\t\t\t\tqr.backgroundColor = '#FFFFFF' // 背景色\r\n\t\t\t\t\t\tqr.errorCorrectLevel = UQRCode.errorCorrectLevel.H // 容错级别\r\n\r\n\t\t\t\t\t\t// 关键修复：先调用make()方法\r\n\t\t\t\t\t\tqr.make()\r\n\r\n\t\t\t\t\t\t// 获取canvas上下文\r\n\t\t\t\t\t\tconst ctx = uni.createCanvasContext('qrcode-canvas', this)\r\n\r\n\t\t\t\t\t\t// 绘制二维码\r\n\t\t\t\t\t\tqr.canvasContext = ctx\r\n\t\t\t\t\t\tqr.drawCanvas()\r\n\r\n\t\t\t\t\t\t// 获取临时图片路径 (微信小程序特殊处理)\r\n\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\tuni.canvasToTempFilePath({\r\n\t\t\t\t\t\t\t\tcanvasId: 'qrcode-canvas',\r\n\t\t\t\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\t\t\t\tthis.qrCodeUrl = res.tempFilePath\r\n\t\t\t\t\t\t\t\t\tthis.showCanvas = false // 生成后隐藏canvas\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\t\t\t\tconsole.error('生成二维码失败', err)\r\n\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\ttitle: '生成二维码失败',\r\n\t\t\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}, this)\r\n\t\t\t\t\t\t}, 300)\r\n\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\tconsole.error('生成二维码异常:', error)\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '生成二维码异常',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 返回上一页\r\n\t\t\tgoback() {\r\n\t\t\t\tuni.navigateBack({\r\n\t\t\t\t\tdelta: 1\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tasync userInfoApi() {\r\n\t\t\t\tlet data = await userInfo()\r\n\t\t\t\tif (data.code == 1) {\r\n\t\t\t\t\tuni.setStorageSync('user', data.data)\r\n\t\t\t\t\tthis.userall = data.data\r\n\t\t\t\t\tconsole.log(this.userall, '用户信息');\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: data.msg,\r\n\t\t\t\t\t\ticon: \"none\"\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// async user_codeApi() {\r\n\t\t\t// \tlet data = await user_code()\r\n\t\t\t// \tthis.img = data.data\r\n\t\t\t// \tconsole.log(this.img, '用户code');\r\n\t\t\t// }\r\n\t\t},\r\n\t\t// onUnload() {\r\n\t\t// \tclearInterval(this.clear1)\r\n\t\t// \tclearInterval(this.clear2)\r\n\t\t// }\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.back {\r\n\t\tposition: absolute;\r\n\t\twidth: 100%;\r\n\t\theight: 100vh;\r\n\t}\r\n\r\n\t.nav-tab {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t\tz-index: 2;\r\n\r\n\t\t.nav-tab_left {\r\n\t\t\tpadding: 30rpx;\r\n\t\t}\r\n\r\n\t\t.nav-tab_content {\r\n\t\t\tfont-size: 34rpx;\r\n\t\t\tfont-weight: 500;\r\n\t\t\tcolor: #FFFFFF;\r\n\t\t\tz-index: 2;\r\n\t\t\ttext-align: center;\r\n\t\t\tpadding-right: 100rpx;\r\n\t\t}\r\n\t}\r\n\r\n\t.qr {\r\n\t\twidth: 700rpx;\r\n\t\theight: 714rpx;\r\n\t\tbackground: #FFFFFF;\r\n\t\tborder-radius: 27rpx;\r\n\t\tposition: absolute;\r\n\t\tleft: 50%;\r\n\t\ttransform: translateX(-50%);\r\n\t\ttop: 30%;\r\n\r\n\t\t.qr_1 {\r\n\t\t\tmargin: 0 auto;\r\n\t\t\ttext-align: center;\r\n\r\n\t\t\t.qr_1_img {\r\n\t\t\t\twidth: 120rpx;\r\n\t\t\t\theight: 120rpx;\r\n\t\t\t\tborder-radius: 50%;\r\n\t\t\t\toverflow: hidden;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\tbottom: 50rpx;\r\n\t\t\t\tmargin: 0 auto;\r\n\t\t\t}\r\n\r\n\t\t\ttext {\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tfont-weight: 400;\r\n\t\t\t\tcolor: #343434;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\tbottom: 50rpx;\r\n\t\t\t}\r\n\r\n\t\t}\r\n\r\n\t\t.qr_2 {\r\n\t\t\twidth: 340rpx;\r\n\t\t\theight: 340rpx;\r\n\t\t\tmargin: 0 auto;\r\n\t\t}\r\n\r\n\t\t.qr_3 {\r\n\t\t\tfont-size: 24rpx;\r\n\t\t\tfont-weight: 400;\r\n\t\t\tcolor: #9B9B9B;\r\n\t\t\ttext-align: center;\r\n\t\t\tmargin-top: 50rpx;\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./codeqr.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./codeqr.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754557256682\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}