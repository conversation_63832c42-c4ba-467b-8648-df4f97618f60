{"version": 3, "sources": ["uni-app:///main.js", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/pages/order_all/goodsordersearch/goodsordersearch.vue?d95c", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/pages/order_all/goodsordersearch/goodsordersearch.vue?d415", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/pages/order_all/goodsordersearch/goodsordersearch.vue?7df0", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/pages/order_all/goodsordersearch/goodsordersearch.vue?f5ad", "uni-app:///pages/order_all/goodsordersearch/goodsordersearch.vue", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/pages/order_all/goodsordersearch/goodsordersearch.vue?6f6a", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/pages/order_all/goodsordersearch/goodsordersearch.vue?0fae"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "showCoupon", "showMask", "show", "currentIndex", "dirIndex", "height", "turnOver", "list", "name", "lists", "dirList", "listIndex", "detail_list", "detailImg", "goodLists", "tempList", "animation", "animationData", "id", "detail", "index_list", "indexActive", "moduleDetail", "classList", "promo_material", "comm_material", "type", "userInfo", "activeTabIndex", "listenList", "listenIndex", "videoList", "user", "onLoad", "onShow", "methods", "goToCart", "uni", "url", "closeMask", "shareClick", "initShare", "console", "listenClick", "studyClick", "listClick", "checkDetail", "getStudentsDetail", "errCode", "msg", "payClick", "carClick", "quantity", "setTimeout", "download", "link", "document", "title", "icon", "handleCopy", "htmlContent", "plainText", "getHtmlContent", "getPlainText", "handleCopys", "getHtmlContents", "getPlainTexts", "modernCopy", "htmlBlob", "textBlob", "navigator", "legacyCopy", "container", "range", "selection", "showToast", "duration", "getDetail", "back", "open", "study", "pay", "chooseItem", "chooseDirItem", "rotate", "created", "timingFunction"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,yBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA6H;AAC7H;AACoE;AACL;AACc;;;AAG7E;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,sFAAM;AACR,EAAE,2FAAM;AACR,EAAE,oGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,+FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qRAEN;AACP,KAAK;AACL;AACA,aAAa,sQAEN;AACP,KAAK;AACL;AACA,aAAa,oSAEN;AACP,KAAK;AACL;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3DA;AAAA;AAAA;AAAA;AAA0mB,CAAgB,ooBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;AC0I9nB;AAIA;AAOA;AAEA;AAAA;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;QACAC;MACA,GACA;QACAA;MACA,EACA;MACAC;QACAD;MACA,GACA;QACAA;MACA,EACA;MACAE;QACAF;MACA,GACA;QACAA;MACA,GACA;QACAA;MACA,GACA;QACAA;MACA,GACA;QACAA;MACA,GACA;QACAA;MACA,EAEA;MACAG;MAAA;MACAC;MAAA;MACAC;MAAA;;MAEAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;IAEA;EACA;EACAC;IACA;IACA;EACA;EACAC,2BAEA;EACAC;IACAC;MACA;MACAC;QACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAC;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAA;gBACAC;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACA;IACAC;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACAP;QACAC;MACA;IACA;IACA;IACAO;MACA;MACA;MACA,gFACA;IAEA;IACA;IACAC;MACA;MACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAMA;cAAA;gBAAA;gBAHAhD;gBACAiD;gBACAC;gBAEA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;oBACA;sBAAA;oBAAA;oBACA;kBACA;oBACA;kBACA;gBACA;kBACAZ;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAIA;IACA;IACAa;MACA;QACA;QACAb;UACAC;QACA;QAEA;MACA;MACA;MACA;MACAD;MACA;QACAA;UACAC;QACA;MACA;QACAD;UACAC;QACA;MACA;IAEA;IACA;IACAa;MAAA;MACA;QAAA;MAAA;MACA;QACA;QACA;MACA;QACA;QACA,8CACA;UACAC;QAAA,EACA;QACA;MACA;;MAEA;MACAf;MACAgB;QACA;MACA;IAEA;IACA;IACAC;MACA;MACA;MACAC;MACAA;MACAA;;MAEA;MACAC;MACAD;MACAC;;MAEA;MACAnB;QACAoB;QACAC;MACA;IACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBACAC,yCACA;gBACAC,qCACA;gBAAA;gBAAA,OACA;cAAA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MAEA;IACA;IAEA;IACAC;MACA;IAEA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBACAJ,2CACA;gBACAC,uCACA;gBAAA;gBAAA,OACA;cAAA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAI;MAEA;IAEA;IAEA;IACAC;MAEA;IACA;IACA;IACAC;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;kBACA1C;gBACA;gBACA2C;kBACA3C;gBACA;gBAEA3B,QACA;kBACA;kBACA;gBACA,GACA;gBAAA;gBAAA,OAEAuE;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MACA;MACAC;MACAA;MACAA;MAEAhB;MAEA;MACAiB;MAEA;MACAC;MACAA;MAEAlB;MAEAkB;MACAlB;IACA;IAEA;IACAmB;MACAtC;QACAoB;QACAC;QACAkB;MACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAMA;cAAA;gBAAA;gBAHA9E;gBACAiD;gBACAC;gBAEA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;gBAGA;kBACAZ;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAIA;IACAyC;MACAzC;IACA;IACA0C;MACA1C;QACAC;MACA;IACA;IACA0C;MACA3C;QACAC;MACA;IACA;IACA2C;MACA5C;QACAC;MACA;IACA;IACA4C;MACA;MACA;QACA;QACA;QACA,8EACA;MACA;IAEA;IACAC;MACA;MACA;MACA;MACAzC;IACA;IACA0C;MACA;QACA;QACA;MACA;QACA;QACA;MACA;MACA;MACA;IACA;EAEA;EACAC;IACA;MACAT;MACAU;IACA;IACA;IACA;IACA;IACA;IACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5jBA;AAAA;AAAA;AAAA;AAAqpC,CAAgB,2nCAAG,EAAC,C;;;;;;;;;;;ACAzqC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/order_all/goodsordersearch/goodsordersearch.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/order_all/goodsordersearch/goodsordersearch.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./goodsordersearch.vue?vue&type=template&id=13b70db2&\"\nvar renderjs\nimport script from \"./goodsordersearch.vue?vue&type=script&lang=js&\"\nexport * from \"./goodsordersearch.vue?vue&type=script&lang=js&\"\nimport style0 from \"./goodsordersearch.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/order_all/goodsordersearch/goodsordersearch.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./goodsordersearch.vue?vue&type=template&id=13b70db2&\"", "var components\ntry {\n  components = {\n    uTabs: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-tabs/u-tabs\" */ \"uview-ui/components/u-tabs/u-tabs.vue\"\n      )\n    },\n    uniCollapse: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/@dcloudio/uni-ui/lib/uni-collapse/uni-collapse\" */ \"@dcloudio/uni-ui/lib/uni-collapse/uni-collapse.vue\"\n      )\n    },\n    uniCollapseItem: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/@dcloudio/uni-ui/lib/uni-collapse-item/uni-collapse-item\" */ \"@dcloudio/uni-ui/lib/uni-collapse-item/uni-collapse-item.vue\"\n      )\n    },\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-popup/u-popup\" */ \"uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.detail_list.length\n  var g1 = _vm.listenList.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./goodsordersearch.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./goodsordersearch.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container\">\r\n\r\n\t\t<view class=\"top-banner\">\r\n\t\t\t<image :src=\"detail.course_cover\" mode=\"widthFix\"></image>\r\n\t\t</view>\r\n\t\t<view class=\"title\">\r\n\t\t\t<text class=\"title-name\">{{detail.name}}</text>\r\n\t\t\t<view class=\"center\">\r\n\t\t\t\t<text>开课时间：{{detail.start_time ||''}}-{{detail.end_time ||''}}</text>\r\n\t\t\t\t<text class=\"u-border-left\">课时：{{detail.hours ||'-'}}节</text>\r\n\t\t\t</view>\r\n\t\t\t<text class=\"money  u-border-bottom\">￥{{detail.checkout_price}}</text>\r\n\t\t</view>\r\n\t\t<view class=\" tabs\">\r\n\t\t\t<view class=\"tabsBox\">\r\n\t\t\t\t<u-tabs :list=\"type==1 ? list : lists\" lineWidth=\"50\" lineHeight=\"10\"\r\n\t\t\t\t\t:lineColor=\"`url(https://yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/preset/dxt/wan.png) 100% 100%`\"\r\n\t\t\t\t\t:activeStyle=\"{\r\n\t\t\t\t        color: '#26C8AC',\r\n\t\t\t\t        fontWeight: 'bold',\r\n\t\t\t\t        transform: 'scale(1.1)'\r\n\t\t\t\t    }\" :inactiveStyle=\"{\r\n\t\t\t\t\t\tfontSize:'28rpx',\r\n\t\t\t\t        color: '#777777',\r\n\t\t\t\t\t\tfontWeight: 'bold',\r\n\t\t\t\t        transform: 'scale(1)'\r\n\t\t\t\t    }\" itemStyle=\" height: 82rpx;\" @click=\"chooseItem\">\r\n\t\t\t\t</u-tabs>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"course-detail\" v-show=\"currentIndex==0\">\r\n\t\t\t\t<template v-if=\"detail_list.length>1\">\r\n\t\t\t\t\t<view class=\"dir-tab\">\r\n\t\t\t\t\t\t<text v-for=\"(item,index) in detail_list\" :key=\"index\" :class=\"listIndex==index ? 'sel' : ''\"\r\n\t\t\t\t\t\t\t@click=\"checkDetail(item.detail,index)\">{{item.cate}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</template>\r\n\t\t\t\t<view class=\"copy-content\">\r\n\r\n\t\t\t\t\t<rich-text :nodes=\"detailImg\"></rich-text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"course-dir padding-dir\" v-show=\"currentIndex==1\">\r\n\t\t\t\t<template v-if=\"listenList.length\">\r\n\t\t\t\t\t<view class=\"dir-tab dir-tab-index\">\r\n\t\t\t\t\t\t<text v-for=\"(item,index) in listenList\" :key=\"index\" :class=\"listenIndex==index ? 'sel' : ''\"\r\n\t\t\t\t\t\t\t@click=\"listenClick(item,index)\">{{item.cate}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- 直播信息 -->\r\n\t\t\t\t\t<view class=\"live-info\">\r\n\t\t\t\t\t\t<view class=\"live-title\">\r\n\t\t\t\t\t\t\t<text class=\"green-block\"></text>\r\n\t\t\t\t\t\t\t<text>试听课</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<!-- <view class=\"live-name\">\r\n\t\t\t\t\t\t精讲最新时政题型总结\r\n\t\t\t\t\t</view> -->\r\n\t\t\t\t\t\t<view class=\"live-date\" v-for=\"(item,index) in videoList\" :key=\"index\">\r\n\t\t\t\t\t\t\t<view class=\"live-name\">\r\n\t\t\t\t\t\t\t\t<text>{{item.title}}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"btn-enter\" @click=\"studyClick(item)\">\r\n\t\t\t\t\t\t\t\t进入试听\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</template>\r\n\r\n\t\t\t\t<!-- 课程列表 -->\r\n\t\t\t\t<view class=\"dir-tab m-t-30 dir-tab-index\">\r\n\t\t\t\t\t<text :class=\"indexActive==index ? 'sel' : ''\" v-for=\"(item,index) in index_list\" :key=\"index\"\r\n\t\t\t\t\t\t@click=\"listClick(item,index)\">{{item.cate}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"dir-tabs\">\r\n\t\t\t\t\t<u-tabs :list=\"moduleDetail\" lineWidth=\"40\" lineHeight=\"10\"\r\n\t\t\t\t\t\t:lineColor=\"`url(https://yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/preset/dxt/wan.png) 100% 100%`\"\r\n\t\t\t\t\t\t:activeStyle=\"{\r\n\t\t\t\t\t        color: '#26C8AC',\r\n\t\t\t\t\t        fontWeight: 'bold',\r\n\t\t\t\t\t        transform: 'scale(1.1)'\r\n\t\t\t\t\t    }\" :inactiveStyle=\"{\r\n\t\t\t\t\t\t\tfontSize:'26rpx',\r\n\t\t\t\t\t        color: '#777777',\r\n\t\t\t\t\t\t\tfontWeight: 'bold',\r\n\t\t\t\t\t        transform: 'scale(1)'\r\n\t\t\t\t\t    }\" itemStyle=\"box-sizing: border-box; padding-right: 20rpx; height: 82rpx;\" :current=\"activeTabIndex\"\r\n\t\t\t\t\t\t@click=\"chooseDirItem\">\r\n\t\t\t\t\t</u-tabs>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"course-container\">\r\n\t\t\t\t\t<uni-collapse v-for=\"(item,index) in classList\" :key=\"index\">\r\n\t\t\t\t\t\t<view class=\"course-radius\">\r\n\t\t\t\t\t\t\t<uni-collapse-item :title=\"item.name\" :show-animation=\"true\">\r\n\t\t\t\t\t\t\t\t<view class=\"content\" v-for=\"(items,k) in item.indexes\" :key=\"k\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"text\">{{items.name}}</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</uni-collapse-item>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t</uni-collapse>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"bottom-opt\">\r\n\t\t\t<view class=\"left\">\r\n\t\t\t\t<text class=\"price\">￥{{detail.checkout_price}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"btnBox\" v-if=\"!user.user\">\r\n\t\t\t\t<view class=\"car\" @click=\"carClick\">\r\n\t\t\t\t\t加入购物车\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- <view class=\"btn\" @click=\"payClick\">\r\n\t\t\t\t\t加入购物车\r\n\t\t\t\t</view> -->\r\n\t\t\t</view>\r\n\r\n\t\t</view>\r\n\r\n\t\t<u-popup :show=\"showCoupon\" mode=\"bottom\" @close=\"close\">\r\n\t\t\t<view class=\"coupon-conainer\">\r\n\t\t\t\t<view class=\"title-img\">\r\n\t\t\t\t\t<image src=\"@/static/good.png\" mode=\"\"></image>\r\n\t\t\t\t\t<text>加入购物车成功</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"btn-bottom\">\r\n\t\t\t\t\t<view class=\"btn\" @click=\"closeMask\">继续逛</view>\r\n\t\t\t\t\t<view class=\"btn\" @click=\"goToCart\">去购物车</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</u-popup>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\t// import store from '@/store/user/index.js'\r\n\timport {\r\n\t\t// getProductDetail,\r\n\t\tgetStuDetail\r\n\t} from '@/api/user.js'\r\n\timport {\r\n\t\tgetProductDetail\r\n\t} from '@/api/comm.js'\r\n\t// \r\n\t// import {\r\n\t// \tgetToken,\r\n\t// } from \"@/utils/storage.js\";\r\n\timport {\r\n\t\tsetupWechatShare\r\n\t} from '@/utils/share.js'\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tshowCoupon: false,\r\n\t\t\t\tshowMask: false,\r\n\t\t\t\tshow: false,\r\n\t\t\t\tcurrentIndex: 0,\r\n\t\t\t\tdirIndex: 0,\r\n\t\t\t\theight: 0,\r\n\t\t\t\tturnOver: false, //折叠动画\r\n\t\t\t\tlist: [{\r\n\t\t\t\t\t\tname: '课程详情'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: '课程目录'\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t\tlists: [{\r\n\t\t\t\t\t\tname: '课程详情'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: '课程目录'\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t\tdirList: [{\r\n\t\t\t\t\t\tname: '导学'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: '夯实基础'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: \"强化提升\"\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: '冲刺阶段'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: '点题阶段'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: \"复试阶段\"\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t],\r\n\t\t\t\tlistIndex: 0, //课程详情目录激活索引\r\n\t\t\t\tdetail_list: [], //课程详情\r\n\t\t\t\tdetailImg: '', //课程详情图片\r\n\r\n\t\t\t\tgoodLists: [], //本地存储的产品\r\n\t\t\t\ttempList: [],\r\n\t\t\t\tanimation: {},\r\n\t\t\t\tanimationData: {},\r\n\t\t\t\tid: '',\r\n\t\t\t\tdetail: {}, //详情\r\n\t\t\t\tindex_list: [], //课程目录\r\n\t\t\t\tindexActive: 0, //课程目录激活索引\r\n\t\t\t\tmoduleDetail: [], //课程目录模块内容\r\n\t\t\t\tclassList: [], //课程章节\r\n\t\t\t\tpromo_material: [], //朋友圈图片\r\n\t\t\t\tcomm_material: [], //社群图片\r\n\t\t\t\ttype: 0, //0代表公开，1代表品牌商\r\n\t\t\t\tuserInfo: {},\r\n\t\t\t\tactiveTabIndex: 0, // 新增变量，用于存储当前激活的 tab 索引\r\n\t\t\t\tlistenList: [], //未登录和学生端试听\r\n\t\t\t\tlistenIndex: 0,\r\n\t\t\t\tvideoList: [],\r\n\t\t\t\tuser: uni.getStorageSync('user') || {},\r\n\r\n\t\t\t};\r\n\t\t},\r\n\t\tonLoad(options) {\r\n\t\t\tthis.id = options.id\r\n\t\t\tthis.getStudentsDetail()\r\n\t\t},\r\n\t\tonShow() {\r\n\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tgoToCart() {\r\n\t\t\t\tthis.showCoupon = false\r\n\t\t\t\tuni.reLaunch({\r\n\t\t\t\t\turl: '/pages/order/order?showCart=true'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 关闭遮罩层\r\n\t\t\tcloseMask() {\r\n\t\t\t\tthis.showCoupon = false\r\n\t\t\t},\r\n\t\t\t// 点击分享\r\n\t\t\tshareClick() {\r\n\t\t\t\tthis.showMask = true\r\n\t\t\t},\r\n\t\t\t// async initData() {\r\n\t\t\t// \tif (getToken() && this.userInfo.role === 'user') {\r\n\t\t\t// \t\tawait this.getDetail();\r\n\t\t\t// \t\tthis.type = 1;\r\n\t\t\t// \t} else {\r\n\t\t\t// \t\tawait this.getStudentsDetail();\r\n\t\t\t// \t\tthis.type = 0;\r\n\t\t\t// \t}\r\n\t\t\t// },\r\n\t\t\tasync initShare(name) {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tawait setupWechatShare(name); // 传入当前页面标题\r\n\t\t\t\t\tconsole.log('分享配置完成');\r\n\t\t\t\t} catch (e) {\r\n\t\t\t\t\tconsole.error('分享配置失败:', e);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t//切换试听科目\r\n\t\t\tlistenClick(item, index) {\r\n\t\t\t\tthis.listenIndex = index\r\n\t\t\t\tthis.videoList = item.videos\r\n\t\t\t},\r\n\t\t\t// 去试听\r\n\t\t\tstudyClick(item) {\r\n\t\t\t\tlet cate = this.listenList[this.listenIndex].name\r\n\t\t\t\t// this.$store.commit('user/getListen', {\r\n\t\t\t\t// \tcate: cate,\r\n\t\t\t\t// \ttitle: item.title,\r\n\t\t\t\t// \turl: item.url\r\n\t\t\t\t// });\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: `/subpkg/study/study`\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 切换课程目录\r\n\t\t\tlistClick(item, index) {\r\n\t\t\t\tthis.indexActive = index\r\n\t\t\t\tthis.moduleDetail = item.periods\r\n\t\t\t\tthis.classList = this.moduleDetail.length ? this.moduleDetail[0].chapters : [],\r\n\t\t\t\t\tthis.activeTabIndex = 0; // 重置激活的 tab 索引为 0\r\n\r\n\t\t\t},\r\n\t\t\t// 切换课程详情目录\r\n\t\t\tcheckDetail(item, index) {\r\n\t\t\t\tthis.detailImg = item\r\n\t\t\t\tthis.listIndex = index\r\n\t\t\t},\r\n\t\t\tasync getStudentsDetail() {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst {\r\n\t\t\t\t\t\tdata,\r\n\t\t\t\t\t\terrCode,\r\n\t\t\t\t\t\tmsg\r\n\t\t\t\t\t} = await getProductDetail(this.id)\r\n\t\t\t\t\tif (errCode == 0) {\r\n\t\t\t\t\t\tthis.detail = data\r\n\t\t\t\t\t\tthis.index_list = data.index_list\r\n\t\t\t\t\t\tthis.detail_list = data.detail_list\r\n\t\t\t\t\t\tthis.detailImg = this.detail_list.length ? this.detail_list[0].detail : ''\r\n\t\t\t\t\t\tthis.moduleDetail = this.index_list.length ? this.index_list[0].periods : []\r\n\t\t\t\t\t\tthis.classList = this.moduleDetail.length ? this.moduleDetail[0].chapters : []\r\n\t\t\t\t\t\tif (data.free_trial.length) {\r\n\t\t\t\t\t\t\tthis.listenList = data.free_trial.filter(item => item.videos.length > 0)\r\n\t\t\t\t\t\t\tthis.videoList = this.listenList[0].videos\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthis.listenList = []\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.tip(msg)\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (e) {\r\n\t\t\t\t\t//TODO handle the exception\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 点击立即支付\r\n\t\t\tpayClick() {\r\n\t\t\t\tif (!getToken()) {\r\n\t\t\t\t\t// this.$store.commit('user/setRedirectPath', '/subpkg/product_detail/product_detail?id=' + this.id)\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: '/subpkg/login/login'\r\n\t\t\t\t\t})\r\n\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\t// \r\n\t\t\t\t// this.$store.commit('user/setPayDetail', this.detail)\r\n\t\t\t\tuni.setStorageSync('payDetail', JSON.stringify(this.detail));\r\n\t\t\t\tif (this.userInfo.role === 'user') {\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: '/subpkg/confirm_order/confirm_order?item=' + JSON.stringify(this.detail)\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: '/subpkg/stu_order/stu_order'\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\r\n\t\t\t},\r\n\t\t\t// 点击加入购物车\r\n\t\t\tcarClick() {\r\n\t\t\t\tconst existingItemIndex = this.goodLists.findIndex(cartItem => cartItem.id === this.detail.id);\r\n\t\t\t\tif (existingItemIndex !== -1) {\r\n\t\t\t\t\t// 商品已存在，增加数量\r\n\t\t\t\t\tthis.goodLists[existingItemIndex].quantity += this.detail.min_quantity;\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 商品不存在，添加新商品\r\n\t\t\t\t\tconst newItem = {\r\n\t\t\t\t\t\t...this.detail,\r\n\t\t\t\t\t\tquantity: this.detail.min_quantity\r\n\t\t\t\t\t};\r\n\t\t\t\t\tthis.goodLists.push(newItem);\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 存储到本地\r\n\t\t\t\tuni.setStorageSync('cartItems', this.goodLists);\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tthis.showCoupon = true\r\n\t\t\t\t}, 1000)\r\n\r\n\t\t\t},\r\n\t\t\t// 下载图片\r\n\t\t\tdownload(url, name) {\r\n\t\t\t\t// 创建隐藏链接\r\n\t\t\t\tconst link = document.createElement('a')\r\n\t\t\t\tlink.href = url // 替换为实际URL\r\n\t\t\t\tlink.download = name + '.zip'\r\n\t\t\t\tlink.style.display = 'none'\r\n\r\n\t\t\t\t// 触发下载\r\n\t\t\t\tdocument.body.appendChild(link)\r\n\t\t\t\tlink.click()\r\n\t\t\t\tdocument.body.removeChild(link)\r\n\r\n\t\t\t\t// 显示反馈\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '开始下载',\r\n\t\t\t\t\ticon: 'success'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 复制朋友圈文本\r\n\t\t\tasync handleCopy(e) {\r\n\t\t\t\ttry {\r\n\t\t\t\t\t// 获取 HTML 内容\r\n\t\t\t\t\tconst htmlContent = this.getHtmlContent(e);\r\n\t\t\t\t\t// 获取纯文本备用\r\n\t\t\t\t\tconst plainText = this.getPlainText(e);\r\n\t\t\t\t\t// 现代浏览器方案\r\n\t\t\t\t\tawait this.modernCopy(htmlContent, plainText);\r\n\t\t\t\t\tthis.showToast('复制成功');\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\t// 传统降级方案\r\n\t\t\t\t\tthis.legacyCopy(htmlContent, plainText);\r\n\t\t\t\t\tthis.showToast('复制成功');\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 获取 DOM 的 HTML\r\n\t\t\tgetHtmlContent(e) {\r\n\r\n\t\t\t\treturn this.$refs.promo_text.$el.innerHTML;\r\n\t\t\t},\r\n\r\n\t\t\t// 获取纯文本内容\r\n\t\t\tgetPlainText() {\r\n\t\t\t\treturn this.$refs.promo_text.$el.innerText;\r\n\r\n\t\t\t},\r\n\t\t\t// 复制社群文本\r\n\t\t\tasync handleCopys(e) {\r\n\t\t\t\ttry {\r\n\t\t\t\t\t// 获取 HTML 内容\r\n\t\t\t\t\tconst htmlContent = this.getHtmlContents(e);\r\n\t\t\t\t\t// 获取纯文本备用\r\n\t\t\t\t\tconst plainText = this.getPlainTexts(e);\r\n\t\t\t\t\t// 现代浏览器方案\r\n\t\t\t\t\tawait this.modernCopy(htmlContent, plainText);\r\n\t\t\t\t\tthis.showToast('复制成功');\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\t// 传统降级方案\r\n\t\t\t\t\tthis.legacyCopy(htmlContent, plainText);\r\n\t\t\t\t\tthis.showToast('复制成功');\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 获取 DOM 的 HTML\r\n\t\t\tgetHtmlContents(e) {\r\n\r\n\t\t\t\treturn this.$refs.comm_text.$el.innerHTML;\r\n\r\n\t\t\t},\r\n\r\n\t\t\t// 获取纯文本内容\r\n\t\t\tgetPlainTexts() {\r\n\r\n\t\t\t\treturn this.$refs.comm_text.$el.innerText;\r\n\t\t\t},\r\n\t\t\t// 现代浏览器 API\r\n\t\t\tasync modernCopy(html, text) {\r\n\t\t\t\tconst htmlBlob = new Blob([html], {\r\n\t\t\t\t\ttype: 'text/html'\r\n\t\t\t\t});\r\n\t\t\t\tconst textBlob = new Blob([text], {\r\n\t\t\t\t\ttype: 'text/plain'\r\n\t\t\t\t});\r\n\r\n\t\t\t\tconst data = [\r\n\t\t\t\t\tnew ClipboardItem({\r\n\t\t\t\t\t\t'text/html': htmlBlob,\r\n\t\t\t\t\t\t'text/plain': textBlob\r\n\t\t\t\t\t})\r\n\t\t\t\t];\r\n\r\n\t\t\t\tawait navigator.clipboard.write(data);\r\n\t\t\t},\r\n\r\n\t\t\t// 传统浏览器降级方案\r\n\t\t\tlegacyCopy(html, text) {\r\n\t\t\t\tconst container = document.createElement('div');\r\n\t\t\t\tcontainer.style.position = 'fixed';\r\n\t\t\t\tcontainer.style.left = '-9999px';\r\n\t\t\t\tcontainer.innerHTML = html;\r\n\r\n\t\t\t\tdocument.body.appendChild(container);\r\n\r\n\t\t\t\tconst range = document.createRange();\r\n\t\t\t\trange.selectNode(container);\r\n\r\n\t\t\t\tconst selection = window.getSelection();\r\n\t\t\t\tselection.removeAllRanges();\r\n\t\t\t\tselection.addRange(range);\r\n\r\n\t\t\t\tdocument.execCommand('copy');\r\n\r\n\t\t\t\tselection.removeAllRanges();\r\n\t\t\t\tdocument.body.removeChild(container);\r\n\t\t\t},\r\n\r\n\t\t\t// 显示提示\r\n\t\t\tshowToast(message) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: message,\r\n\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\tduration: 2000\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tasync getDetail() {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst {\r\n\t\t\t\t\t\tdata,\r\n\t\t\t\t\t\terrCode,\r\n\t\t\t\t\t\tmsg\r\n\t\t\t\t\t} = await getProductDetail(this.id)\r\n\t\t\t\t\tif (errCode == 0) {\r\n\t\t\t\t\t\tthis.detail = data\r\n\t\t\t\t\t\tthis.index_list = data.index_list\r\n\t\t\t\t\t\tthis.detail_list = data.detail_list\r\n\t\t\t\t\t\tthis.detailImg = this.detail_list.length ? this.detail_list[0].detail : ''\r\n\t\t\t\t\t\tthis.moduleDetail = this.index_list.length ? this.index_list[0].periods : []\r\n\t\t\t\t\t\tthis.classList = this.moduleDetail.length ? this.moduleDetail[0].chapters : []\r\n\r\n\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.tip(msg)\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (e) {\r\n\t\t\t\t\t//TODO handle the exception\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tback() {\r\n\t\t\t\tuni.navigateBack()\r\n\t\t\t},\r\n\t\t\topen() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/subpkg/open_lesson/open_lesson'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tstudy() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: \"/choosecoursepkg/study/study\"\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tpay() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: \"\"\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tchooseItem(item) {\r\n\t\t\t\tthis.currentIndex = item.index\r\n\t\t\t\tif (item.index == 1) {\r\n\t\t\t\t\t//重新计算 子tab\r\n\t\t\t\t\tthis.tempList = [...this.dirList]\r\n\t\t\t\t\tthis.moduleDetail = this.index_list.length ? this.index_list[0].periods : [],\r\n\t\t\t\t\t\tthis.classList = this.moduleDetail.length ? this.moduleDetail[0].chapters : []\r\n\t\t\t\t}\r\n\r\n\t\t\t},\r\n\t\t\tchooseDirItem(item) {\r\n\t\t\t\tthis.classList = item.chapters\r\n\t\t\t\tthis.dirIndex = item.index\r\n\t\t\t\tthis.activeTabIndex = item.index\r\n\t\t\t\tconsole.log(this.activeTabIndex)\r\n\t\t\t},\r\n\t\t\trotate() {\r\n\t\t\t\tif (!this.turnOver) {\r\n\t\t\t\t\tthis.animation.rotate(-180).step()\r\n\t\t\t\t\tthis.show = false;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.animation.rotate(0).step()\r\n\t\t\t\t\tthis.show = true;\r\n\t\t\t\t}\r\n\t\t\t\tthis.animationData = this.animation.export()\r\n\t\t\t\tthis.turnOver = !this.turnOver\r\n\t\t\t}\r\n\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\tvar animation = uni.createAnimation({\r\n\t\t\t\tduration: 500,\r\n\t\t\t\ttimingFunction: 'ease',\r\n\t\t\t})\r\n\t\t\tthis.animation = animation\r\n\t\t\t// this.$store.dispatch('user/getReportData')\r\n\t\t\t// 加载本地存储的购物车数据\r\n\t\t\tconst cartData = uni.getStorageSync('cartItems');\r\n\t\t\tif (cartData) {\r\n\t\t\t\tthis.goodLists = cartData;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t/* 颜色变量 */\r\n\t$primary-green: #07C160; // 主绿色\r\n\t$price-red: #FF3B30; // 价格红\r\n\t$dark-text: #333333; // 主文字\r\n\t$light-text: #666666; // 次要文字\r\n\t$border-color: #E5E5E5; // 分割线颜色\r\n\r\n\t.container {\r\n\t\t.course-detail {\r\n\t\t\tpadding: 0 30rpx;\r\n\t\t\tbox-sizing: border-box;\r\n\t\t}\r\n\r\n\t\t::v-deep .u-popup__content {\r\n\t\t\tborder-radius: 16rpx 16rpx 0rpx 0rpx;\r\n\t\t}\r\n\r\n\t\t.coupon-conainer {\r\n\t\t\tposition: relative;\r\n\t\t\theight: 400rpx;\r\n\t\t}\r\n\r\n\t\t@keyframes moveUp {\r\n\t\t\t0% {\r\n\t\t\t\ttransform: translate(-50%, 0);\r\n\t\t\t}\r\n\r\n\t\t\t50% {\r\n\t\t\t\ttransform: translate(-50%, -220rpx);\r\n\t\t\t}\r\n\r\n\t\t\t100% {\r\n\t\t\t\ttransform: translate(-50%, -100rpx);\r\n\t\t\t\t/* 上移80px */\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.title-img {\r\n\t\t\tposition: absolute;\r\n\t\t\t/* 初始位置 */\r\n\t\t\ttop: 250rpx;\r\n\t\t\t/* 可根据需要调整 */\r\n\t\t\tleft: 50%;\r\n\t\t\ttransform: translateX(-50%);\r\n\r\n\t\t\t/* 动画设置 */\r\n\t\t\tanimation: moveUp 1s ease-in-out forwards;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\t\t\tfont-size: 32rpx;\r\n\t\t\tcolor: #4A4A4C;\r\n\r\n\t\t\timage {\r\n\t\t\t\twidth: 50rpx;\r\n\t\t\t\theight: 50rpx;\r\n\t\t\t\tmargin-right: 20rpx;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.btn-bottom {\r\n\t\t\tposition: fixed;\r\n\t\t\tbottom: 10rpx;\r\n\t\t\twidth: 690rpx;\r\n\t\t\tleft: 50%;\r\n\t\t\ttransform: translateX(-50%);\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\r\n\t\t\t.btn {\r\n\t\t\t\tflex: 1;\r\n\t\t\t\theight: 80rpx;\r\n\t\t\t\tline-height: 80rpx;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tbackground-color: #26C8AC;\r\n\t\t\t\tborder-radius: 0 36rpx 36rpx 0;\r\n\r\n\t\t\t\t&:first-child {\r\n\t\t\t\t\tcolor: #26C8AC;\r\n\t\t\t\t\tbackground-color: #E7FBF7;\r\n\t\t\t\t\tborder-radius: 36rpx 0 0 36rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.custom-navbar {\r\n\t\t\theight: 88rpx;\r\n\t\t\tpadding: 0 32rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tbackground-color: #fff;\r\n\t\t\tborder-bottom: 1rpx solid #E5E5E5;\r\n\r\n\t\t\t.nav-icon {\r\n\t\t\t\twidth: 48rpx;\r\n\t\t\t\theight: 48rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.nav-title {\r\n\t\t\t\tflex: 1;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tfont-size: 36rpx;\r\n\t\t\t\tcolor: #333333;\r\n\t\t\t\tfont-weight: 500;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.tabsBox {\r\n\t\t\tbackground-color: #fff;\r\n\t\t\tmargin-bottom: 20rpx;\r\n\t\t}\r\n\r\n\t\t::v-deep .u-border-left {\r\n\t\t\theight: 25rpx !important;\r\n\t\t\tline-height: 20rpx;\r\n\t\t}\r\n\r\n\t\t::v-deep .u-border-bottom {\r\n\t\t\tborder-bottom: none !important;\r\n\t\t}\r\n\r\n\t\t/deep/ .u-tabs__wrapper__nav__line {\r\n\t\t\twidth: 40px !important;\r\n\t\t\theight: 10px !important;\r\n\t\t}\r\n\r\n\t\t::v-deep .u-tabs__wrapper__nav__item {\r\n\t\t\tpadding: 0 !important;\r\n\t\t\twidth: 25% !important;\r\n\t\t\ttext-align: center !important;\r\n\t\t}\r\n\r\n\t\t::v-deep .uni-collapse-item__title.uni-collapse-item-border {\r\n\t\t\tborder-bottom: none !important;\r\n\t\t}\r\n\r\n\t\t::v-deep .uni-collapse {\r\n\t\t\tborder-radius: 20rpx !important;\r\n\t\t}\r\n\r\n\t\t::v-deep .uni-collapse-item__wrap-content.uni-collapse-item--border {\r\n\t\t\tborder: none !important;\r\n\t\t}\r\n\r\n\t\t::v-deep .uni-collapse-item__wrap-content {\r\n\t\t\t.content {\r\n\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\tcolor: #4A4A4C;\r\n\t\t\t\tpadding: 25rpx 35rpx;\r\n\t\t\t\tbox-sizing: border-box;\r\n\t\t\t\tborder-top: 1rpx solid #B9B9B9;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t::v-deep .uni-collapse-item__title {\r\n\r\n\t\t\tborder-radius: 24rpx !important;\r\n\t\t}\r\n\r\n\t\t::v-deep .uni-collapse-item__title-arrow {\r\n\t\t\twidth: 30rpx;\r\n\t\t\theight: 30rpx;\r\n\t\t\tborder-radius: 50%;\r\n\t\t\tbackground: #26C8AC;\r\n\r\n\t\t\t.uni-icons {\r\n\t\t\t\tcolor: #fff !important;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t::v-deep.uni-collapse-item__title.is-open {\r\n\t\t\tborder-radius: 24rpx 24rpx 0 0 !important;\r\n\t\t}\r\n\r\n\t\t::v-deep .uni-collapse-item__title-text {\r\n\t\t\tfont-weight: bold;\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tcolor: #4A4A4C;\r\n\t\t\tmargin-left: 30rpx;\r\n\r\n\t\t}\r\n\r\n\t\t::v-deep .uni-collapse-item__title-box {\r\n\t\t\tposition: relative;\r\n\r\n\t\t\t&:before {\r\n\t\t\t\tcontent: '';\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\twidth: 14rpx;\r\n\t\t\t\theight: 28rpx;\r\n\t\t\t\ttop: 50%;\r\n\t\t\t\ttransform: translateY(-50%);\r\n\t\t\t\tleft: 30rpx;\r\n\t\t\t\tbackground: #26C8AC;\r\n\t\t\t\tz-index: 999;\r\n\t\t\t\tborder-radius: 0rpx 8rpx 0rpx 8rpx;\r\n\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tview {\r\n\t\t\tbox-sizing: border-box;\r\n\t\t}\r\n\r\n\t\t.m-t-30 {\r\n\t\t\tmargin-top: 30rpx;\r\n\t\t}\r\n\r\n\t\tpadding-bottom: 120rpx;\r\n\r\n\t\t.h1-title {\r\n\t\t\twidth: 100%;\r\n\t\t\tpadding: 26rpx 0;\r\n\t\t\tcolor: #201E2E;\r\n\t\t\ttext-align: left;\r\n\t\t\tfont-weight: bold;\r\n\t\t\tfont-size: 32rpx;\r\n\t\t\ttext-align: left;\r\n\t\t}\r\n\r\n\t\tbackground-color: #F6F7FB;\r\n\t\tmin-height: 100vh;\r\n\t\t// padding-top: 20rpx;\r\n\r\n\t\t.top-banner {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\r\n\t\t\timage {\r\n\t\t\t\twidth: 625rpx;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.title {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\tflex-direction: column;\r\n\t\t\tbackground-color: #fff;\r\n\t\t\tpadding: 0 30rpx;\r\n\t\t\tpadding-top: 22rpx;\r\n\t\t\twidth: 100%;\r\n\t\t\ttext-align: left;\r\n\t\t\tcolor: #060606;\r\n\t\t\tfont-size: 32rpx;\r\n\t\t\tfont-weight: bold;\r\n\r\n\t\t\t.title-name {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t}\r\n\r\n\t\t\t.center {\r\n\t\t\t\tfont-weight: normal;\r\n\t\t\t\tmargin-top: 12rpx;\r\n\t\t\t\tcolor: #777777;\r\n\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: flex-start;\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\ttext-align: left;\r\n\r\n\t\t\t\ttext {\r\n\t\t\t\t\t&:first-child {\r\n\t\t\t\t\t\tpadding-right: 13rpx;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t&:last-child {\r\n\t\t\t\t\t\tpadding-left: 13rpx;\r\n\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.money {\r\n\t\t\t\tmargin-top: 25rpx;\r\n\t\t\t\tdisplay: inline-block;\r\n\t\t\t\tcolor: #E16965;\r\n\t\t\t\tfont-size: 34rpx;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\ttext-align: left;\r\n\t\t\t\tpadding-bottom: 22rpx;\r\n\t\t\t\tfont-weight: 400 !important;\r\n\t\t\t}\r\n\r\n\t\t\t.last {\r\n\t\t\t\tbackground-color: #fffae8;\r\n\t\t\t\tpadding: 15rpx;\r\n\t\t\t\tborder-radius: 20rpx;\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\tmargin-bottom: 20rpx;\r\n\t\t\t\tcolor: #706E6E;\r\n\t\t\t\tfont-weight: bold;\r\n\r\n\t\t\t}\r\n\r\n\t\t\t.extra-info {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\tcolor: #706E6E;\r\n\t\t\t\tfont-weight: normal;\r\n\t\t\t\tpadding: 26rpx 0;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: flex-start;\r\n\r\n\t\t\t\ttext {\r\n\t\t\t\t\tmargin-right: 8rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t// $main-color: #009c7b;\r\n\t\t\t// $container-bg-color: #F6F7FB;\r\n\r\n\t\t\t.course-list {\r\n\t\t\t\tmargin-bottom: 22rpx;\r\n\t\t\t\tbackground-color: #fff;\r\n\t\t\t\tborder-radius: 16rpx;\r\n\t\t\t\twidth: 100%;\r\n\r\n\t\t\t\t.mid {\r\n\t\t\t\t\tmargin-top: 6rpx;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tjustify-content: flex-start;\r\n\r\n\t\t\t\t\t.tag {\r\n\t\t\t\t\t\tpadding: 0 8rpx;\r\n\t\t\t\t\t\theight: 48rpx;\r\n\t\t\t\t\t\tbackground-color: #EEFAF6;\r\n\t\t\t\t\t\tcolor: #009c7b;\r\n\t\t\t\t\t\tline-height: 48rpx;\r\n\t\t\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.date {\r\n\t\t\t\t\t\tmargin-left: 8rpx;\r\n\t\t\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\t\t\tcolor: #A4A4A4;\r\n\t\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.bottom {\r\n\t\t\t\t\tmargin-top: 20rpx;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tjustify-content: space-between;\r\n\r\n\t\t\t\t\t.teacher-list {\r\n\t\t\t\t\t\tpadding-left: 36rpx;\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\tjustify-content: flex-start;\r\n\t\t\t\t\t\tpadding-bottom: 20rpx;\r\n\r\n\t\t\t\t\t\t.teacher-info {\r\n\t\t\t\t\t\t\tmargin-right: 24rpx;\r\n\t\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\t\tflex-direction: column;\r\n\t\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\t\t\t\tcolor: #818181;\r\n\r\n\t\t\t\t\t\t\t.avatar {\r\n\t\t\t\t\t\t\t\twidth: 60rpx;\r\n\t\t\t\t\t\t\t\theight: 60rpx;\r\n\t\t\t\t\t\t\t\tborder-radius: 100%;\r\n\t\t\t\t\t\t\t\tmargin-bottom: 2rpx;\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\ttext {\r\n\t\t\t\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.course-money {\r\n\t\t\t\t\t\tcolor: #E16965;\r\n\t\t\t\t\t\tfont-size: 38rpx;\r\n\t\t\t\t\t\tmargin-right: 34rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t}\r\n\r\n\r\n\t\t\t.teachers {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tpadding-bottom: 26rpx;\r\n\r\n\t\t\t\t.all-teacher-info {\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\twhite-space: nowrap;\r\n\r\n\t\t\t\t\t.teacher-item {\r\n\t\t\t\t\t\twidth: 30%;\r\n\t\t\t\t\t\tdisplay: inline-block;\r\n\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\tjustify-content: space-between;\r\n\t\t\t\t\t\tflex-direction: column;\r\n\r\n\t\t\t\t\t\t.top-info {\r\n\t\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\t\tjustify-content: center;\r\n\r\n\t\t\t\t\t\t\timage {\r\n\t\t\t\t\t\t\t\twidth: 80rpx;\r\n\t\t\t\t\t\t\t\theight: 80rpx;\r\n\t\t\t\t\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t\t\t\t\tmargin-right: 6rpx;\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\ttext {\r\n\t\t\t\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t.teacher-extra {\r\n\t\t\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\t\t\tflex-direction: column;\r\n\t\t\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\t\t\tjustify-content: space-between;\r\n\r\n\t\t\t\t\t\t\t\ttext {\r\n\t\t\t\t\t\t\t\t\tfont-size: 22rpx;\r\n\r\n\t\t\t\t\t\t\t\t\t&:first-child {\r\n\t\t\t\t\t\t\t\t\t\tcolor: #818181;\r\n\r\n\t\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\t\t&:last-child {\r\n\t\t\t\t\t\t\t\t\t\tcolor: #201E2E;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t.bottom-info {\r\n\t\t\t\t\t\t\tmargin-top: 6rpx;\r\n\t\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\t\tjustify-content: space-between;\r\n\t\t\t\t\t\t\tflex-direction: column;\r\n\r\n\t\t\t\t\t\t\ttext {\r\n\t\t\t\t\t\t\t\tcolor: #201E2E;\r\n\t\t\t\t\t\t\t\tfont-size: 16rpx;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.tabs {\r\n\t\t\twidth: 100%;\r\n\t\t\tmargin-top: 16rpx;\r\n\r\n\t\t}\r\n\r\n\t\t.course-dir {\r\n\t\t\tbackground-color: #F6F7FB;\r\n\t\t\tpadding: 0 30rpx;\r\n\r\n\t\t\t.dir-tab {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: flex-start;\r\n\r\n\t\t\t\ttext {\r\n\t\t\t\t\twidth: 110rpx;\r\n\t\t\t\t\theight: 60rpx;\r\n\t\t\t\t\tborder-radius: 12rpx;\r\n\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\tline-height: 60rpx;\r\n\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\tcolor: #201E2E;\r\n\t\t\t\t\tbackground-color: #fff;\r\n\t\t\t\t\tmargin-right: 20rpx;\r\n\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.sel {\r\n\t\t\t\t\tbackground: #26C8AC;\r\n\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.dir-tab-index {\r\n\t\t\t\tmargin-bottom: 0;\r\n\t\t\t\tpadding: 0 30rpx;\r\n\t\t\t\tbox-sizing: border-box;\r\n\t\t\t}\r\n\r\n\t\t\t.live-info {\r\n\t\t\t\tmargin-top: 30rpx;\r\n\t\t\t\tbackground-color: #fff;\r\n\t\t\t\tborder-radius: 20rpx;\r\n\t\t\t\tpadding: 30rpx 28rpx;\r\n\r\n\t\t\t\t.live-title {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tjustify-content: flex-start;\r\n\t\t\t\t\tcolor: #0A0A0A;\r\n\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t\tfont-weight: bold;\r\n\r\n\t\t\t\t\t.green-block {\r\n\t\t\t\t\t\twidth: 14rpx;\r\n\t\t\t\t\t\theight: 28rpx;\r\n\t\t\t\t\t\tline-height: 28rpx;\r\n\t\t\t\t\t\tbackground: #01997A;\r\n\t\t\t\t\t\tborder-radius: 0rpx 8rpx 0rpx 8rpx;\r\n\t\t\t\t\t\tmargin-right: 6rpx;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.live-name {\r\n\t\t\t\t\tcolor: #4A4A4C;\r\n\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t\ttext-align: left;\r\n\t\t\t\t\t// margin-top: 24rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.live-date {\r\n\t\t\t\t\tmargin-top: 20rpx;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tjustify-content: space-between;\r\n\r\n\t\t\t\t\t.date {\r\n\t\t\t\t\t\tcolor: #A2A2A2;\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\r\n\t\t\t\t\t\ttext {\r\n\t\t\t\t\t\t\tmargin-right: 16rpx;\r\n\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.btn-enter {\r\n\t\t\t\t\t\twidth: 129rpx;\r\n\t\t\t\t\t\theight: 44rpx;\r\n\t\t\t\t\t\tbackground: linear-gradient(90deg, #10C19D 0%, #16AC8E 100%);\r\n\t\t\t\t\t\tborder-radius: 28rpx;\r\n\t\t\t\t\t\tline-height: 44rpx;\r\n\t\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.dir-tabs {\r\n\r\n\t\t\t\t// margin-top: 32rpx;\r\n\t\t\t\t/deep/ .u-tabs__wrapper__nav__line {\r\n\t\t\t\t\twidth: 40px !important;\r\n\t\t\t\t\theight: 10px !important;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t::v-deep .u-tabs__wrapper__nav__item {\r\n\t\t\t\t\tpadding: 0 !important;\r\n\t\t\t\t\twidth: 20% !important;\r\n\t\t\t\t\ttext-align: center !important;\r\n\t\t\t\t}\r\n\r\n\t\t\t}\r\n\r\n\t\t\t.course-container {\r\n\t\t\t\twidth: 690rpx;\r\n\t\t\t\tmargin: 30rpx auto;\r\n\t\t\t\tbackground-color: #fff;\r\n\t\t\t\t// border-radius: 20rpx;\r\n\r\n\t\t\t\t.title-name {\r\n\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tjustify-content: space-between;\r\n\t\t\t\t\tpadding: 30rpx 28rpx;\r\n\r\n\t\t\t\t\t.left {\r\n\t\t\t\t\t\tcolor: #4A4A4C;\r\n\t\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\t\tfont-weight: bold;\r\n\r\n\t\t\t\t\t\t.green-block {\r\n\t\t\t\t\t\t\tdisplay: inline-block;\r\n\t\t\t\t\t\t\twidth: 14rpx;\r\n\t\t\t\t\t\t\theight: 28rpx;\r\n\t\t\t\t\t\t\tline-height: 28rpx;\r\n\t\t\t\t\t\t\tbackground: #01997A;\r\n\t\t\t\t\t\t\tborder-radius: 0rpx 8rpx 0rpx 8rpx;\r\n\t\t\t\t\t\t\tmargin-right: 6rpx;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.expand {\r\n\t\t\t\t\t\twidth: 30rpx;\r\n\t\t\t\t\t\theight: 30rpx;\r\n\t\t\t\t\t\tbackground: #01997A;\r\n\t\t\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\ttransition: transform 0.5s ease;\r\n\t\t\t\t\t\tjustify-content: center;\r\n\r\n\t\t\t\t\t\t.rotate {\r\n\t\t\t\t\t\t\ttransform: rotate(180deg);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.course-list {\r\n\t\t\t\theight: auto;\r\n\t\t\t\toverflow: hidden;\r\n\t\t\t\ttransition: height 0.8s ease;\r\n\r\n\t\t\t\t.list-item {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tjustify-content: space-between;\r\n\t\t\t\t\tpadding: 30rpx 28rpx;\r\n\r\n\t\t\t\t\t.title-text {\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\tjustify-content: flex-start;\r\n\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t\tcolor: #4A4A4C;\r\n\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.enter {\r\n\t\t\t\t\t\twidth: 130rpx;\r\n\t\t\t\t\t\theight: 44rpx;\r\n\t\t\t\t\t\tbackground: linear-gradient(90deg, #10C19D 0%, #16AC8E 100%);\r\n\t\t\t\t\t\tborder-radius: 60rpx;\r\n\t\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\t\tline-height: 44rpx;\r\n\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.padding-dir {\r\n\t\t\tpadding: 0;\r\n\t\t}\r\n\r\n\t\t.bottom-opt {\r\n\t\t\twidth: 100%;\r\n\t\t\tpadding: 0 22rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\tposition: fixed;\r\n\t\t\tbottom: 0;\r\n\t\t\theight: 120rpx;\r\n\t\t\tbackground-color: #fff;\r\n\t\t\tz-index: 9999;\r\n\t\t\tbox-shadow: 0rpx -6rpx 6rpx 1rpx rgba(180, 180, 180, 0.16);\r\n\r\n\t\t\t.left {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: space-between;\r\n\r\n\t\t\t\t.price {\r\n\t\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\t\tcolor: #E16965;\r\n\t\t\t\t\tmargin-right: 24rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t}\r\n\r\n\t\t\t.btnBox {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t}\r\n\r\n\r\n\t\t\t.btn,\r\n\t\t\t.car {\r\n\t\t\t\twidth: 204rpx;\r\n\t\t\t\theight: 66rpx;\r\n\t\t\t\tbackground: linear-gradient(268deg, #26C8AC 0%, #19C990 100%);\r\n\t\t\t\tline-height: 66rpx;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\tcolor: #FFFFFF;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tborder-radius: 40rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.car {\r\n\t\t\t\tbackground: linear-gradient(268deg, #26C8AC 0%, #19C990 100%);\r\n\t\t\t\tmargin-right: 24rpx;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.dir-tab {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: flex-start;\r\n\t\t\t// margin-left: 30rpx;\r\n\t\t\tmargin-bottom: 30rpx;\r\n\r\n\t\t\ttext {\r\n\t\t\t\twidth: 110rpx;\r\n\t\t\t\theight: 60rpx;\r\n\t\t\t\tborder-radius: 12rpx;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tline-height: 60rpx;\r\n\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t\tcolor: #201E2E;\r\n\t\t\t\tbackground-color: #fff;\r\n\t\t\t\tmargin-right: 20rpx;\r\n\r\n\t\t\t}\r\n\r\n\t\t\t.sel {\r\n\t\t\t\tbackground: #26C8AC;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.copy-content {\r\n\t\t\twidth: 690rpx;\r\n\t\t\t// margin: 20rpx auto;\r\n\t\t\tbox-sizing: border-box;\r\n\t\t\t// padding: 20rpx 30rpx 20rpx 40rpx;\r\n\t\t\tbox-sizing: border-box;\r\n\t\t\tborder-radius: 30rpx;\r\n\t\t\tbackground-color: #fff;\r\n\r\n\r\n\r\n\t\t\tp {\r\n\t\t\t\t::v-deep img {\r\n\t\t\t\t\twidth: 100% !important;\r\n\t\t\t\t\tborder-radius: 20rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.copy-title {\r\n\t\t\t\twidth: 170rpx;\r\n\t\t\t\theight: 40rpx;\r\n\t\t\t\tline-height: 36rpx;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\tcolor: #01997A;\r\n\t\t\t\tborder-radius: 363rpx;\r\n\t\t\t\tborder: 1rpx solid #01997A;\r\n\t\t\t\t// 新增样式让元素靠右对齐\r\n\t\t\t\tmargin-left: auto;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\tmargin-bottom: 10rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.content-list {\r\n\t\t\t\tfont-weight: 400;\r\n\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\tcolor: #4A4A4C;\r\n\t\t\t\tline-height: 1.5;\r\n\t\t\t\tmargin-bottom: 20rpx;\r\n\r\n\t\t\t\t.question {\r\n\t\t\t\t\tposition: relative;\r\n\r\n\t\t\t\t\t&:before {\r\n\t\t\t\t\t\tcontent: \"\";\r\n\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\twidth: 8rpx;\r\n\t\t\t\t\t\theight: 8rpx;\r\n\t\t\t\t\t\ttop: 18rpx;\r\n\t\t\t\t\t\tleft: -15rpx;\r\n\t\t\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t\t\tbackground: #01997A;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.answer {\r\n\t\t\t\t\tmargin-top: 5rpx;\r\n\t\t\t\t\tcolor: #01997A;\r\n\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.friends-content {\r\n\t\t\twidth: 690rpx;\r\n\t\t\tmargin: 20rpx auto;\r\n\t\t\tbox-sizing: border-box;\r\n\t\t\tpadding: 20rpx 30rpx 20rpx 40rpx;\r\n\t\t\tbox-sizing: border-box;\r\n\t\t\tborder-radius: 20rpx;\r\n\t\t\tbackground-color: #fff;\r\n\r\n\t\t\t.friends-title {\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tcolor: #404040;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tmargin-bottom: 15rpx;\r\n\r\n\t\t\t\t.green {\r\n\t\t\t\t\twidth: 11rpx;\r\n\t\t\t\t\theight: 30rpx;\r\n\t\t\t\t\tbackground: #01997A;\r\n\t\t\t\t\tmargin-right: 8rpx;\r\n\t\t\t\t\tborder-radius: 0rpx 8rpx 0rpx 8rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.title-copy {\r\n\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\tcolor: #01997A;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: space-between;\r\n\r\n\t\t\t\ttext {\r\n\t\t\t\t\tmargin-left: 20rpx;\r\n\t\t\t\t\tposition: relative;\r\n\r\n\t\t\t\t\t// &:before {\r\n\t\t\t\t\t// \tcontent: \"\";\r\n\t\t\t\t\t// \tposition: absolute;\r\n\t\t\t\t\t// \twidth: 8rpx;\r\n\t\t\t\t\t// \theight: 8rpx;\r\n\t\t\t\t\t// \ttop: 18rpx;\r\n\t\t\t\t\t// \tleft: -20rpx;\r\n\t\t\t\t\t// \tborder-radius: 50%;\r\n\t\t\t\t\t// \tbackground: #01997A;\r\n\t\t\t\t\t// }\r\n\t\t\t\t}\r\n\r\n\r\n\t\t\t}\r\n\r\n\t\t\t.copyText {\r\n\t\t\t\twidth: 140rpx;\r\n\t\t\t\tcolor: #01997A;\r\n\t\t\t\theight: 45rpx;\r\n\t\t\t\tline-height: 45rpx;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tborder-radius: 363rpx;\r\n\t\t\t\tborder: 1rpx solid #01997A;\r\n\t\t\t}\r\n\r\n\t\t\t.contentText {\r\n\t\t\t\twidth: 400rpx;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tline-height: 2;\r\n\t\t\t}\r\n\r\n\t\t\t.contentImg {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: flex-start;\r\n\t\t\t\tjustify-content: space-between;\r\n\t\t\t\tmargin-top: 15rpx;\r\n\t\t\t\tmargin-bottom: 20rpx;\r\n\r\n\t\t\t\t.image-box {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tflex-wrap: wrap;\r\n\t\t\t\t\twidth: 400rpx;\r\n\r\n\t\t\t\t\timage {\r\n\t\t\t\t\t\twidth: 120rpx;\r\n\t\t\t\t\t\theight: 120rpx;\r\n\t\t\t\t\t\tmargin-right: 10rpx;\r\n\t\t\t\t\t\tmargin-bottom: 10rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.mask {\r\n\t\t\tposition: fixed;\r\n\t\t\ttop: 0;\r\n\t\t\tleft: 0;\r\n\t\t\twidth: 100vw;\r\n\t\t\theight: 100vh;\r\n\t\t\tbackground-color: rgba(0, 0, 0, 0.6);\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-direction: column;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\t\t\tz-index: 99999;\r\n\r\n\t\t\t.share {\r\n\t\t\t\twidth: 340rpx;\r\n\t\t\t\theight: 360rpx;\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tright: 40rpx;\r\n\t\t\t\ttop: 20rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./goodsordersearch.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./goodsordersearch.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754557257045\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}