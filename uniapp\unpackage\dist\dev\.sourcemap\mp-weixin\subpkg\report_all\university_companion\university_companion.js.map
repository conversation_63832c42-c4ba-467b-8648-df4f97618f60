{"version": 3, "sources": ["uni-app:///main.js", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/subpkg/report_all/university_companion/university_companion.vue?fc03", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/subpkg/report_all/university_companion/university_companion.vue?e703", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/subpkg/report_all/university_companion/university_companion.vue?6349", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/subpkg/report_all/university_companion/university_companion.vue?bfb5", "uni-app:///subpkg/report_all/university_companion/university_companion.vue", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/subpkg/report_all/university_companion/university_companion.vue?dd66", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/subpkg/report_all/university_companion/university_companion.vue?f3a9"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "currentDate", "tasks", "readRate", "doneRate", "progressImg", "progressPink", "flag", "taskDetail", "downLoadName", "list", "title", "content", "image", "showMask", "sugestionText", "historySugestionList", "isCurrentMonth", "currentSuggestion", "currentTaskId", "currentMaterial", "purposeList", "suggestionList", "lastSuggestion", "methods", "close", "is<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "downLoad", "getDownloadUrl", "errCode", "msg", "uni", "url", "icon", "directDownload", "link", "document", "upload", "showDetail", "material", "fileName", "suggestion", "purpose", "attachment", "id", "showAllSugesstion", "getCurrentYearMonth", "formatDisplayDate", "year", "month", "checkIsCurrentMonth", "handleDateChange", "fetchMonthData", "yearMonth", "res", "name", "status", "taskId", "isRead", "createTime", "console", "length", "handleTaskDone", "taskMaterialId", "filters", "pad<PERSON><PERSON>", "computed", "components", "userTitle", "created", "onShow"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,6BAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA6I;AAC7I;AACwE;AACL;AACsC;;;AAGzG;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,0FAAM;AACR,EAAE,2GAAM;AACR,EAAE,oHAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,+GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACzCA;AAAA;AAAA;AAAA;AAA8mB,CAAgB,woBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;AC4LloB;AAEA;EAAA;IAAA;EAAA;AAAA;AAAA,eACA;EACAC;IACA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MAAA;MACAC;QACAC;QACAC;UACAC;UACAC;UACAC;QACA;MACA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;;EACAC;IACA;IACAC;MACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;IACA;IACA;IACAC;MACA;QACA;QACA;QACA;QACA;MACA;QACA;QACA;MACA;IACA;IACAC;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAMA;cAAA;gBAAA;gBAHA5B;gBACA6B;gBACAC;gBAEA;kBACAC;oBACAC;kBACA;gBACA;kBACAD;oBACApB;oBACAsB;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAF;kBACApB;kBACAsB;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACA;IACAC;MACA;MACAC;MACAA;MACAC;MACAD;MACAC;IACA;IACA;IACAC;MACA;QACA;MACA;QACAN;UACAC;QACA;MACA;IAEA;IACAM;MACA;MACA;MACA;MACA;MACA,sCACAC;QAAA;MAAA;;MAEA;MACA,4CACAA;QAAA;MAAA;;MAEA;MACA;QACA9B,kCACA8B,kFACAC;QACA9B;UACAE;UACA6B;UACAC;UACA7B;UACA8B;UACAH;UACAI;QACA;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;IACA;IACA;IACAC;MACA;QAAA;QAAAC;QAAAC;MACA;IACA;IACA;IACAC;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IAEA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,OAKArB;kBACAsB;gBACA;cAAA;gBAFAC;gBAIA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;;kBAEA;kBACA;oBACA;oBACA;oBACA;oBACA;sBAAA;wBACAV;wBACAW;wBACAC;wBACAhB;wBACA3B;wBACA4B;wBACAC;wBACAC;wBACAc;wBACAC;sBACA;oBAAA;oBACA;oBACA;kBACA;kBACA;kBACA;kBACA;oBACA;oBACA;oBACA;sBAAA;wBACAC;wBACA/C;wBACAC;sBACA;oBAAA;oBACA+C;oBACA,gFACAC;kBACA;gBACA;kBACA9B;oBACApB;oBACAsB;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA2B;gBACA7B;kBACApB;kBACAsB;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACA;IACA6B;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;kBACA;kBACA;kBACA;kBACA;gBACA;gBAAA;gBAAA;gBAAA,OAEA/B;kBACA0B;kBACAM;gBACA;cAAA;gBAHAT;gBAKA;kBAEA;kBACA;kBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAM;gBACA7B;kBACApB;kBACAsB;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;EACA;EACA+B;IACAC;MACA;QACA;MACA;MACA;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EAAA,CACA;EACAC;IACAC;EACA;EACAC;IACAT;IACA;IACA;IACA;MACA;IACA;EACA;EACAU;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxeA;AAAA;AAAA;AAAA;AAAirC,CAAgB,upCAAG,EAAC,C;;;;;;;;;;;ACArsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "subpkg/report_all/university_companion/university_companion.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './subpkg/report_all/university_companion/university_companion.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./university_companion.vue?vue&type=template&id=8e603e4a&scoped=true&\"\nvar renderjs\nimport script from \"./university_companion.vue?vue&type=script&lang=js&\"\nexport * from \"./university_companion.vue?vue&type=script&lang=js&\"\nimport style0 from \"./university_companion.vue?vue&type=style&index=0&id=8e603e4a&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"8e603e4a\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"subpkg/report_all/university_companion/university_companion.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./university_companion.vue?vue&type=template&id=8e603e4a&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = !_vm.isCurrentMonth ? _vm.formatDisplayDate(_vm.currentDate) : null\n  var l0 = _vm.__map(_vm.tasks, function (task, index) {\n    var $orig = _vm.__get_orig(task)\n    var f0 = _vm._f(\"padNum\")(index + 1)\n    return {\n      $orig: $orig,\n      f0: f0,\n    }\n  })\n  var g0 = _vm.lastSuggestion.length\n  var g1 =\n    _vm.showMask && _vm.flag == 0 && _vm.taskDetail.list.suggestion\n      ? _vm.suggestionList.length\n      : null\n  var g2 =\n    _vm.showMask && _vm.flag == 0 && _vm.taskDetail.list.purpose\n      ? _vm.purposeList.length\n      : null\n  var g3 =\n    _vm.showMask && _vm.flag == 1 ? _vm.historySugestionList.length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        l0: l0,\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./university_companion.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./university_companion.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container\">\r\n\r\n\t\t<!-- 当月任务 -->\r\n\t\t<view class=\"task-section\">\r\n\t\t\t<view class=\"task-header\">\r\n\t\t\t\t<view class=\"tip\" :style=\"{ visibility: showMask ? 'hidden' : 'visible' }\">\r\n\t\t\t\t\t<text class=\"tip-text\">{{ isCurrentMonth ? '当月任务' : formatDisplayDate(currentDate) + '任务' }}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<picker mode=\"date\" fields=\"month\" :value=\"currentDate\" @change=\"handleDateChange\">\r\n\t\t\t\t\t<image src=\"https://yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/h5/calendar.png\"\r\n\t\t\t\t\t\tstyle=\"height: 60rpx; width: 60rpx;\"></image>\r\n\t\t\t\t</picker>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"title\">\r\n\t\t\t\t<view class=\"tag\">\r\n\t\t\t\t\t接收率\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"progress\">\r\n\t\t\t\t\t<view class=\"overlay\" :style=\"{\r\n\t\t\t\t\t\twidth: readRate + '%',\r\n\t\t\t\t\t\tbackgroundImage: `url(${progressImg})`,\r\n\t\t\t\t\t}\">\r\n\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"title-info\">\r\n\t\t\t\t\t{{ readRate }}%\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"title\">\r\n\t\t\t\t<view class=\"tag\">\r\n\t\t\t\t\t完成率\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"progress\">\r\n\t\t\t\t\t<view class=\"overlay\" :style=\"{\r\n\t\t\t\t\t\twidth: doneRate + '%',\r\n\t\t\t\t\t\tbackgroundImage: `url(${progressPink})`\r\n\t\t\t\t\t}\">\r\n\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"title-info\">\r\n\t\t\t\t\t{{ doneRate }}%\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<!-- 任务列表 -->\r\n\t\t\t<view class=\"task-list\">\r\n\t\t\t\t<view v-for=\"(task, index) in tasks\" :key=\"index\" class=\"task-item\">\r\n\t\t\t\t\t<text class=\"task-index\">{{ index + 1 | padNum }}</text>\r\n\t\t\t\t\t<view class=\"task-title\">\r\n\t\t\t\t\t\t<text>{{ task.name }}</text>\r\n\t\t\t\t\t\t<view class=\"btnBox\">\r\n\t\t\t\t\t\t\t<button @click=\"upload(task)\" class=\"detail\" v-if=\"task.status==0\">上传</button>\r\n\t\t\t\t\t\t\t<button @click=\"showDetail(task)\" class=\"detail\">详情</button>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"tagBox\">\r\n\t\t\t\t\t\t<view class=\"tag\" v-if=\"task.isRead\">已接收</view>\r\n\t\t\t\t\t\t<view class=\"tag tags\" v-if=\"task.status==1\">已完成</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 综合建议 -->\r\n\t\t<view class=\"suggestion-section\">\r\n\t\t\t<view class=\"task-header\">\r\n\t\t\t\t<view class=\"tip\">\r\n\t\t\t\t\t<text class=\"tip-text\">综合建议</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<image @click=\"showAllSugesstion\"\r\n\t\t\t\t\tsrc=\"https://yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/h5/university_companion_records.png\"\r\n\t\t\t\t\tstyle=\"height: 60rpx; width: 60rpx;\"></image>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view v-if=\"lastSuggestion.length>0\" class=\"suggestion-content\">\r\n\t\t\t\t<view v-for=\"(item, index) in lastSuggestion\" :key=\"index\">\r\n\t\t\t\t\t<rich-text :nodes=\"item.content\"></rich-text>\r\n\t\t\t\t\t<view class=\"imgs\" v-if=\"item.image\">\r\n\t\t\t\t\t\t<image class=\"img\" :src=\"item.image\" mode=\"aspectFill\">\r\n\t\t\t\t\t\t</image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\r\n\t\t\t</view>\r\n\t\t\t<view v-else class=\"suggestion-content\">\r\n\t\t\t\t<text class=\"line\">暂无建议</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<view class=\"mask\" v-if=\"showMask\" @tap.self=\"close\">\r\n\t\t\t<view class=\"content\">\r\n\t\t\t\t<view class=\"content-scroll\">\r\n\t\t\t\t\t<template v-if=\"flag == 0\">\r\n\t\t\t\t\t\t<view class=\"title\">\r\n\t\t\t\t\t\t\t详细内容\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"task-content\">\r\n\t\t\t\t\t\t\t<view class=\"tip\">\r\n\t\t\t\t\t\t\t\t<text class=\"tip-text\">任务主题</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"task-content-text\">\r\n\t\t\t\t\t\t\t\t{{ taskDetail.list.content }}\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"task-content\" v-if=\"taskDetail.list.suggestion\">\r\n\t\t\t\t\t\t\t<view class=\"tip\">\r\n\t\t\t\t\t\t\t\t<text class=\"tip-text\">完成建议</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"task-content-list-text\" v-if=\"suggestionList.length\">\r\n\t\t\t\t\t\t\t\t<text v-for=\"(text, i) in suggestionList\" :key=\"i\">\r\n\t\t\t\t\t\t\t\t\t{{ text }}\r\n\t\t\t\t\t\t\t\t</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"task-content-text\" v-else>\r\n\t\t\t\t\t\t\t\t{{ taskDetail.list.suggestion }}\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"task-content\" v-if=\"taskDetail.list.purpose\">\r\n\t\t\t\t\t\t\t<view class=\"tip\">\r\n\t\t\t\t\t\t\t\t<text class=\"tip-text\">任务作用</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"task-content-list-text\" v-if=\"purposeList.length\">\r\n\t\t\t\t\t\t\t\t<text v-for=\"(text, i) in purposeList\" :key=\"i\">\r\n\t\t\t\t\t\t\t\t\t{{ text }}\r\n\t\t\t\t\t\t\t\t</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"task-content-text\" v-else>\r\n\t\t\t\t\t\t\t\t{{ taskDetail.list.purpose }}\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"task-content\" v-if=\"taskDetail.list.image\">\r\n\t\t\t\t\t\t\t<view class=\"task-content-image\">\r\n\t\t\t\t\t\t\t\t<image :src=\"taskDetail.list.image\" mode=\"widthFix\"></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"down-load\" v-if=\"taskDetail.list.attachment\">\r\n\t\t\t\t\t\t\t<view class=\"down-load-icon\">\r\n\t\t\t\t\t\t\t\t<image class=\"icon\" src=\"../../static/imgs/zip_icon.png\" mode=\"\"></image>\r\n\t\t\t\t\t\t\t\t<text class=\"text\">{{ taskDetail.downLoadName }}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<button class=\"down-load-btn\" @click=\"downLoad(taskDetail.list)\">下载</button>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t\t<template v-if=\"flag == 1\">\r\n\t\t\t\t\t\t<view class=\"history-list\">\r\n\t\t\t\t\t\t\t<view v-if=\"historySugestionList.length === 0\" class=\"history-item\">\r\n\t\t\t\t\t\t\t\t<view class=\"history-content\">\r\n\t\t\t\t\t\t\t\t\t暂无历史建议记录\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view v-else class=\"history-item\" v-for=\"(item, i) in historySugestionList\" :key=\"i\">\r\n\t\t\t\t\t\t\t\t<view class=\"history-date\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"blue-block\">\r\n\r\n\t\t\t\t\t\t\t\t\t</text>\r\n\t\t\t\t\t\t\t\t\t<text>{{ item.createTime }}</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t\t\t<rich-text :nodes=\"item.content\"></rich-text>\r\n\r\n\t\t\t\t\t\t\t\t<image :src=\"item.image\"></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view v-if=\"flag == 0\" class=\"bottom-btn-container\">\r\n\t\t\t\t\t<button class=\"bottom-btn\" :class=\"{ 'bottom-btn-disabled': currentMaterial.isRead === true }\"\r\n\t\t\t\t\t\t@click=\"handleTaskDone\">\r\n\t\t\t\t\t\t{{ currentMaterial.isRead === true ? '已知晓' : '已知晓阅读' }}\r\n\t\t\t\t\t</button>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n<script>\r\n\timport userTitle from \"@/components/user_title.vue\"\r\n\t// import http from '@/http/index.js'\r\n\timport {\r\n\t\tgetEncryptedAttachment\r\n\t} from '@/api/user.js'\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tcurrentDate: this.getCurrentYearMonth(), // 当前选择的年月\r\n\t\t\t\ttasks: [], // 当月任务列表\r\n\t\t\t\treadRate: 0, //已接收百分比\r\n\t\t\t\tdoneRate: 0, //已完成百分比\r\n\t\t\t\tprogressImg: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAALIAAAAQCAIAAAA9PLLaAAAACXBIWXMAAAsTAAALEwEAmpwYAAAFyWlUWHRYTUw6Y29tLmFkb2JlLnhtcAAAAAAAPD94cGFja2V0IGJlZ2luPSLvu78iIGlkPSJXNU0wTXBDZWhpSHpyZVN6TlRjemtjOWQiPz4gPHg6eG1wbWV0YSB4bWxuczp4PSJhZG9iZTpuczptZXRhLyIgeDp4bXB0az0iQWRvYmUgWE1QIENvcmUgOS4wLWMwMDAgNzkuMTcxYzI3ZiwgMjAyMi8wOC8xNi0xODowMjo0MyAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczpkYz0iaHR0cDovL3B1cmwub3JnL2RjL2VsZW1lbnRzLzEuMS8iIHhtbG5zOnBob3Rvc2hvcD0iaHR0cDovL25zLmFkb2JlLmNvbS9waG90b3Nob3AvMS4wLyIgeG1sbnM6eG1wTU09Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9tbS8iIHhtbG5zOnN0RXZ0PSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvc1R5cGUvUmVzb3VyY2VFdmVudCMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIDI0LjAgKFdpbmRvd3MpIiB4bXA6Q3JlYXRlRGF0ZT0iMjAyNS0wMy0xMlQxNzo1NToyNyswODowMCIgeG1wOk1vZGlmeURhdGU9IjIwMjUtMDMtMTNUMTU6MDc6MzgrMDg6MDAiIHhtcDpNZXRhZGF0YURhdGU9IjIwMjUtMDMtMTNUMTU6MDc6MzgrMDg6MDAiIGRjOmZvcm1hdD0iaW1hZ2UvcG5nIiBwaG90b3Nob3A6Q29sb3JNb2RlPSIzIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOjBlNzY1ZDc5LWRmYjQtNjg0OC1hNDhhLWNjZjU2Yzc5OGMzNSIgeG1wTU06RG9jdW1lbnRJRD0iYWRvYmU6ZG9jaWQ6cGhvdG9zaG9wOmQyOTM3MWY0LTQzMGItYTE0NS04ODM1LTE1N2VmYjlhNDE3ZSIgeG1wTU06T3JpZ2luYWxEb2N1bWVudElEPSJ4bXAuZGlkOjA4ZWIxZmExLTRiYjktYmU0YS04NWZkLWY5YjczZTc0ODYzMiI+IDx4bXBNTTpIaXN0b3J5PiA8cmRmOlNlcT4gPHJkZjpsaSBzdEV2dDphY3Rpb249ImNyZWF0ZWQiIHN0RXZ0Omluc3RhbmNlSUQ9InhtcC5paWQ6MDhlYjFmYTEtNGJiOS1iZTRhLTg1ZmQtZjliNzNlNzQ4NjMyIiBzdEV2dDp3aGVuPSIyMDI1LTAzLTEyVDE3OjU1OjI3KzA4OjAwIiBzdEV2dDpzb2Z0d2FyZUFnZW50PSJBZG9iZSBQaG90b3Nob3AgMjQuMCAoV2luZG93cykiLz4gPHJkZjpsaSBzdEV2dDphY3Rpb249InNhdmVkIiBzdEV2dDppbnN0YW5jZUlEPSJ4bXAuaWlkOjBlNzY1ZDc5LWRmYjQtNjg0OC1hNDhhLWNjZjU2Yzc5OGMzNSIgc3RFdnQ6d2hlbj0iMjAyNS0wMy0xM1QxNTowNzozOCswODowMCIgc3RFdnQ6c29mdHdhcmVBZ2VudD0iQWRvYmUgUGhvdG9zaG9wIDI0LjAgKFdpbmRvd3MpIiBzdEV2dDpjaGFuZ2VkPSIvIi8+IDwvcmRmOlNlcT4gPC94bXBNTTpIaXN0b3J5PiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/PoJ4TNQAAAGPSURBVGiB7dnRSsMwFAbgP2mLbEwKs0MFoV7tzXwsX2zgcFeWTcaE6uxqU+9k0qamOUkWtf/d2PpTsq+Hw8aKYlkU90GQgZBqtam3r/rXj6Jofl1zTrmH1jAhykWG/cF483FqzqP5JUZnNsqpZ6uWIE3YdPL1kuX5HWNCu44JUT6skb/r39FgQp6TmAAQUk0Qz30wIc+pTAAItet8NoF9US7XOFTmm49iz4SBGayWVhPQZkE3UXMepYktE4uMidp88/eEt4ktE/bnHAA2O281AUDnWzFjwtLsdWUiSBMWj43XujMxnQQ3F7J3+08L8nz+IyYkzxklTk2kSccHerIgn/tgQhZ/TKAfC49N1C9vH4+b32vCzY4MNRPowcJnE9u8Wj0z472NWDThZM4pmoDqyum9CeO1zfwfE1CZFvT5PJiQxksT+JEFfT4PJqTx1QS6WdDP3Z4Jke3E0854bTOWTDjbkTVMoIOFzybc/FkAeyZc7ch6JiBbOY3MZ0u/DTszwa9ieyaM1zbD4rGeCQCfzjlgfxElgyUAAAAASUVORK5CYII=\",\r\n\t\t\t\tprogressPink: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAALUAAAATCAIAAABZdNsNAAAACXBIWXMAAAsTAAALEwEAmpwYAAAFyWlUWHRYTUw6Y29tLmFkb2JlLnhtcAAAAAAAPD94cGFja2V0IGJlZ2luPSLvu78iIGlkPSJXNU0wTXBDZWhpSHpyZVN6TlRjemtjOWQiPz4gPHg6eG1wbWV0YSB4bWxuczp4PSJhZG9iZTpuczptZXRhLyIgeDp4bXB0az0iQWRvYmUgWE1QIENvcmUgOS4wLWMwMDAgNzkuMTcxYzI3ZiwgMjAyMi8wOC8xNi0xODowMjo0MyAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczpkYz0iaHR0cDovL3B1cmwub3JnL2RjL2VsZW1lbnRzLzEuMS8iIHhtbG5zOnBob3Rvc2hvcD0iaHR0cDovL25zLmFkb2JlLmNvbS9waG90b3Nob3AvMS4wLyIgeG1sbnM6eG1wTU09Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9tbS8iIHhtbG5zOnN0RXZ0PSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvc1R5cGUvUmVzb3VyY2VFdmVudCMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIDI0LjAgKFdpbmRvd3MpIiB4bXA6Q3JlYXRlRGF0ZT0iMjAyNS0wMy0xMlQxNzo1NToyNyswODowMCIgeG1wOk1vZGlmeURhdGU9IjIwMjUtMDMtMTNUMTU6MTQ6NDUrMDg6MDAiIHhtcDpNZXRhZGF0YURhdGU9IjIwMjUtMDMtMTNUMTU6MTQ6NDUrMDg6MDAiIGRjOmZvcm1hdD0iaW1hZ2UvcG5nIiBwaG90b3Nob3A6Q29sb3JNb2RlPSIzIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOjZlM2U5MjJhLWQ5MjgtNWI0My05NzQyLWEyZjkwNDQ2NjAwZCIgeG1wTU06RG9jdW1lbnRJRD0iYWRvYmU6ZG9jaWQ6cGhvdG9zaG9wOmZiNmE2M2RiLWZlMTUtODM0ZS1iNzVlLWExYjZhYTVjMDEzNCIgeG1wTU06T3JpZ2luYWxEb2N1bWVudElEPSJ4bXAuZGlkOjNjMDk3MGNmLWI1MGYtMmE0MS04MGY2LWU2MTMyZjIwYzA5YSI+IDx4bXBNTTpIaXN0b3J5PiA8cmRmOlNlcT4gPHJkZjpsaSBzdEV2dDphY3Rpb249ImNyZWF0ZWQiIHN0RXZ0Omluc3RhbmNlSUQ9InhtcC5paWQ6M2MwOTcwY2YtYjUwZi0yYTQxLTgwZjYtZTYxMzJmMjBjMDlhIiBzdEV2dDp3aGVuPSIyMDI1LTAzLTEyVDE3OjU1OjI3KzA4OjAwIiBzdEV2dDpzb2Z0d2FyZUFnZW50PSJBZG9iZSBQaG90b3Nob3AgMjQuMCAoV2luZG93cykiLz4gPHJkZjpsaSBzdEV2dDphY3Rpb249InNhdmVkIiBzdEV2dDppbnN0YW5jZUlEPSJ4bXAuaWlkOjZlM2U5MjJhLWQ5MjgtNWI0My05NzQyLWEyZjkwNDQ2NjAwZCIgc3RFdnQ6d2hlbj0iMjAyNS0wMy0xM1QxNToxNDo0NSswODowMCIgc3RFdnQ6c29mdHdhcmVBZ2VudD0iQWRvYmUgUGhvdG9zaG9wIDI0LjAgKFdpbmRvd3MpIiBzdEV2dDpjaGFuZ2VkPSIvIi8+IDwvcmRmOlNlcT4gPC94bXBNTTpIaXN0b3J5PiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/PsOaGtsAAAHTSURBVGiB7dqxroIwFAbgU0TExY34AMYJJ0fjyASTI2/IyFRfwPgEbDq5YeLgZPUq3NWIYG1Pe0su/yr+Afpx0gQIO50sQkAijNJ7lgn/3fK8YRwT15U5h7cpGbskSXE8ojc/hzjOMI6t8VhFueS95YwbhvZs9vYnWwZHyRhL08fhINzQ4WjIn+MAAFu4V34BOhwNMQEHCPuQXwAyGinCUeQ5W6/bi0N+KnPmIw4Q84GAw3GGq5UiHJckKa9X9OaXDIJAEQ4Nkw8A7Pn8Iw4AsL7txcGh5snThoPnyROIPhy+7wYB15Ff9RZ5fknT8nwWOiuADkd9tOKIIt6D+XvlF6DDURczcQC/D5NxPHY7Rml7cejZUMP3OIDTh8k47lnGKEWvrUYdDj2TTwAH8OxPOxzwX3HAx/khP7o7HHUxHwc0+5BfgA5HXVqBAxp8mIzjZ7u9bjbotdUowqFtQy2JA+p8mIxDz4sJUIZD2+STxwFv96coF+BGUatx9BeLVuPoTSbyOKA6P1BGtxuGvelUsqQabThs3x8sl+i12nBYnoeCA158oCyAorGsEwfWzX2OThyIL8btot8vb7ceIcbiKBljlD72e9zat1GEQ9uGGv2Tml9pPru6kycBOAAAAABJRU5ErkJggg==\",\r\n\t\t\t\tflag: 0, //详情还是综合建议 0,是详情 1,综合建议\r\n\t\t\t\ttaskDetail: {\r\n\t\t\t\t\tdownLoadName: '',\r\n\t\t\t\t\tlist: {\r\n\t\t\t\t\t\ttitle: '',\r\n\t\t\t\t\t\tcontent: '',\r\n\t\t\t\t\t\timage: ''\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\tshowMask: false,\r\n\t\t\t\tsugestionText: '', // 当前建议文本\r\n\t\t\t\thistorySugestionList: [], // 历史建议列表\r\n\t\t\t\tisCurrentMonth: true, // 是否是当前月份\r\n\t\t\t\tcurrentSuggestion: null, // 当前显示的建议\r\n\t\t\t\tcurrentTaskId: '', // 当前任务ID\r\n\t\t\t\tcurrentMaterial: null, // 当前查看的材料\r\n\t\t\t\tpurposeList: [], // 任务作用列表\r\n\t\t\t\tsuggestionList: [], // 完成建议列表\r\n\t\t\t\tlastSuggestion: [], //最后一条完成建议\r\n\t\t\t};\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 关闭弹框\r\n\t\t\tclose() {\r\n\t\t\t\tif (this.flag == 1) {\r\n\t\t\t\t\tthis.showMask = false\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 在页面中检测环境\r\n\t\t\tisWeixinBrowser() {\r\n\t\t\t\tconst ua = navigator.userAgent.toLowerCase();\r\n\t\t\t\treturn ua.includes('micromessenger');\r\n\t\t\t},\r\n\t\t\t// 点击下载\r\n\t\t\tdownLoad(item) {\r\n\t\t\t\tif (this.isWeixinBrowser()) {\r\n\t\t\t\t\t// 微信环境下显示提示并引导浏览器打开\r\n\t\t\t\t\t// 关键步骤：对URL进行编码\r\n\t\t\t\t\t// const encodedUrl = encodeURIComponent(fileUrl.attachment);\r\n\t\t\t\t\tthis.getDownloadUrl(item.id)\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 非微信环境直接下载\r\n\t\t\t\t\tthis.directDownload(item);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tasync getDownloadUrl(id) {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst {\r\n\t\t\t\t\t\tdata,\r\n\t\t\t\t\t\terrCode,\r\n\t\t\t\t\t\tmsg\r\n\t\t\t\t\t} = await getEncryptedAttachment(id)\r\n\t\t\t\t\tif (errCode == 0) {\r\n\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\turl: `/subpkg/download/download?urlPath=${data}`\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: msg,\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (e) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: msg,\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 直接下载方法\r\n\t\t\tdirectDownload(url) {\r\n\t\t\t\tconst link = document.createElement('a');\r\n\t\t\t\tlink.href = url.attachment;\r\n\t\t\t\tlink.download = url.fileName; // 设置默认文件名\r\n\t\t\t\tdocument.body.appendChild(link);\r\n\t\t\t\tlink.click();\r\n\t\t\t\tdocument.body.removeChild(link);\r\n\t\t\t},\r\n\t\t\t// 点击上传\r\n\t\t\tupload(item) {\r\n\t\t\t\tif (!item.isRead) {\r\n\t\t\t\t\tthis.showDetail(item)\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: `/subpkg/upload_content/upload_content?id=${item.id}&taskId=${item.taskId}`\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\r\n\t\t\t},\r\n\t\t\tshowDetail(material) {\r\n\t\t\t\tthis.flag = 0\r\n\t\t\t\t// 保存当前查看的材料\r\n\t\t\t\tthis.currentMaterial = material\r\n\t\t\t\t// 处理任务作用文本，如果包含换行符则转为数组\r\n\t\t\t\tthis.purposeList = material.purpose ?\r\n\t\t\t\t\tmaterial.purpose.split('\\n').filter(item => item.trim()) : []\r\n\r\n\t\t\t\t// 处理完成建议文本\r\n\t\t\t\tthis.suggestionList = material.suggestion ?\r\n\t\t\t\t\tmaterial.suggestion.split('\\n').filter(item => item.trim()) : []\r\n\r\n\t\t\t\t// 设置任务详情\r\n\t\t\t\tthis.taskDetail = {\r\n\t\t\t\t\tdownLoadName: material.fileName ?\r\n\t\t\t\t\t\t(material.fileName.length > 10 ? material.fileName.slice(0, 10) + '...' : material\r\n\t\t\t\t\t\t\t.fileName) : '附件',\r\n\t\t\t\t\tlist: {\r\n\t\t\t\t\t\tcontent: material.name,\r\n\t\t\t\t\t\tsuggestion: material.suggestion,\r\n\t\t\t\t\t\tpurpose: material.purpose,\r\n\t\t\t\t\t\timage: material.image || '',\r\n\t\t\t\t\t\tattachment: material.attachment,\r\n\t\t\t\t\t\tfileName: material.fileName,\r\n\t\t\t\t\t\tid: material.id\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tthis.showMask = true\r\n\t\t\t},\r\n\t\t\tshowAllSugesstion() {\r\n\t\t\t\tthis.flag = 1\r\n\t\t\t\tthis.showMask = true\r\n\t\t\t},\r\n\t\t\t// 获取当前年月\r\n\t\t\tgetCurrentYearMonth() {\r\n\t\t\t\tconst date = new Date()\r\n\t\t\t\treturn `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`\r\n\t\t\t},\r\n\t\t\t// 格式化显示日期\r\n\t\t\tformatDisplayDate(dateStr) {\r\n\t\t\t\tconst [year, month] = dateStr.split('-')\r\n\t\t\t\treturn `${year}年${month}月`\r\n\t\t\t},\r\n\t\t\t// 检查是否是当前月份\r\n\t\t\tcheckIsCurrentMonth(dateStr) {\r\n\t\t\t\tconst currentDate = this.getCurrentYearMonth()\r\n\t\t\t\treturn currentDate === dateStr\r\n\t\t\t},\r\n\t\t\t// 处理日期选择变化\r\n\t\t\thandleDateChange(e) {\r\n\t\t\t\tthis.currentDate = e.detail.value\r\n\t\t\t\tthis.isCurrentMonth = this.checkIsCurrentMonth(this.currentDate)\r\n\t\t\t\t// 获取选中月份的数据\r\n\t\t\t\tthis.fetchMonthData(this.currentDate)\r\n\t\t\t},\r\n\t\t\t// 获取指定月份的数据\r\n\t\t\tasync fetchMonthData(yearMonth) {\r\n\t\t\t\t// 检查登录状态，未登录直接返回\r\n\t\t\t\tif (!this.$store.getters.token) {\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst res = await uni.http.get('/stu/studentTask/listByMonth', {\r\n\t\t\t\t\t\tyearMonth\r\n\t\t\t\t\t})\r\n\r\n\t\t\t\t\tif (res.errCode === 0) {\r\n\t\t\t\t\t\t// 初始化数据为空\r\n\t\t\t\t\t\tthis.tasks = []\r\n\t\t\t\t\t\tthis.completionRate = 0\r\n\t\t\t\t\t\tthis.currentTaskId = ''\r\n\t\t\t\t\t\tthis.sugestionText = ''\r\n\t\t\t\t\t\tthis.historySugestionList = []\r\n\t\t\t\t\t\tthis.currentSuggestion = null\r\n\r\n\t\t\t\t\t\t// const taskData = res.data.data[0]\r\n\t\t\t\t\t\tif (res.data.data.length > 0) {\r\n\t\t\t\t\t\t\t// 保存当前任务ID\r\n\t\t\t\t\t\t\t// this.currentTaskId = taskData.id\r\n\t\t\t\t\t\t\t// 设置任务列表\r\n\t\t\t\t\t\t\tthis.tasks = res.data.data.map(item => ({\r\n\t\t\t\t\t\t\t\tid: item.id,\r\n\t\t\t\t\t\t\t\tname: item.name,\r\n\t\t\t\t\t\t\t\tstatus: item.status,\r\n\t\t\t\t\t\t\t\tfileName: item.fileName,\r\n\t\t\t\t\t\t\t\timage: item.image,\r\n\t\t\t\t\t\t\t\tsuggestion: item.suggestion,\r\n\t\t\t\t\t\t\t\tpurpose: item.purpose,\r\n\t\t\t\t\t\t\t\tattachment: item.attachment,\r\n\t\t\t\t\t\t\t\ttaskId: item.taskId,\r\n\t\t\t\t\t\t\t\tisRead: item.isRead\r\n\t\t\t\t\t\t\t}))\r\n\t\t\t\t\t\t\tthis.doneRate = res.data.stats.doneRate\r\n\t\t\t\t\t\t\tthis.readRate = res.data.stats.readRate\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthis.lastSuggestion = []\r\n\t\t\t\t\t\t// 处理建议数据\r\n\t\t\t\t\t\tif (res.data.suggestions && res.data.suggestions.length > 0) {\r\n\t\t\t\t\t\t\tthis.sugestionText = res.data.suggestions[0].suggestion\r\n\t\t\t\t\t\t\tthis.currentSuggestion = res.data.suggestions[0]\r\n\t\t\t\t\t\t\tthis.historySugestionList = res.data.suggestions.map(item => ({\r\n\t\t\t\t\t\t\t\tcreateTime: item.createTime || '',\r\n\t\t\t\t\t\t\t\tcontent: item.suggestion.replace(/\\n/g, \"<br>\"),\r\n\t\t\t\t\t\t\t\timage: item.suggestImage\r\n\t\t\t\t\t\t\t}))\r\n\t\t\t\t\t\t\tconsole.log(this.historySugestionList)\r\n\t\t\t\t\t\t\tthis.lastSuggestion.push(this.historySugestionList[this.historySugestionList\r\n\t\t\t\t\t\t\t\t.length - 1])\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: res.msg,\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (e) {\r\n\t\t\t\t\tconsole.error('获取月度数据失败:', e)\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '获取数据失败',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 处理任务完成\r\n\t\t\tasync handleTaskDone() {\r\n\t\t\t\tif (this.currentMaterial.isRead == true) {\r\n\t\t\t\t\t// 关闭弹窗\r\n\t\t\t\t\tthis.showMask = false\r\n\t\t\t\t\t// 重新获取任务列表\r\n\t\t\t\t\tthis.fetchMonthData(this.currentDate)\r\n\t\t\t\t}\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst res = await uni.http.post('/stu/studentTask/subTaskRead', {\r\n\t\t\t\t\t\ttaskId: this.currentMaterial.taskId,\r\n\t\t\t\t\t\ttaskMaterialId: this.currentMaterial.id\r\n\t\t\t\t\t})\r\n\r\n\t\t\t\t\tif (res.errCode === 0) {\r\n\r\n\t\t\t\t\t\t// 关闭弹窗\r\n\t\t\t\t\t\tthis.showMask = false\r\n\t\t\t\t\t\t// 重新获取任务列表\r\n\t\t\t\t\t\tthis.fetchMonthData(this.currentDate)\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (e) {\r\n\t\t\t\t\tconsole.error('提交任务完成失败:', e)\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '提交失败',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t},\r\n\t\tfilters: {\r\n\t\t\tpadNum(val) {\r\n\t\t\t\tif (parseInt(val) < 10) {\r\n\t\t\t\t\treturn '0' + val\r\n\t\t\t\t}\r\n\t\t\t\treturn val;\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\t// textList() {\r\n\t\t\t// \tlet list = []\r\n\t\t\t// \twhile (this.sugestionText.length > 20) {\r\n\t\t\t// \t\tconst text = this.sugestionText.slice(0, 20)\r\n\t\t\t// \t\tlist.push(text)\r\n\t\t\t// \t\tthis.sugestionText = text\r\n\t\t\t// \t}\r\n\t\t\t// \tif (this.sugestionText.length > 0) {\r\n\t\t\t// \t\tlist.push(this.sugestionText)\r\n\t\t\t// \t}\r\n\t\t\t// \treturn list;\r\n\t\t\t// }\r\n\t\t},\r\n\t\tcomponents: {\r\n\t\t\tuserTitle\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\tconsole.log('un', uni.getStorageSync('TOKEN'))\r\n\t\t\tthis.isCurrentMonth = this.checkIsCurrentMonth(this.currentDate)\r\n\t\t\t// 只在登录状态下获取数据\r\n\t\t\tif (uni.getStorageSync('TOKEN')) {\r\n\t\t\t\tthis.fetchMonthData(this.currentDate)\r\n\t\t\t}\r\n\t\t},\r\n\t\tonShow() {\r\n\t\t\t// if (!uni.getStorageSync('TOKEN')) {\r\n\t\t\t// \t// 保存当前页面路径\r\n\t\t\t// \t// this.$store.commit('user/setRedirectPath', '/pages/university_companion/university_companion')\r\n\t\t\t// \tuni.navigateTo({\r\n\t\t\t// \t\turl: '/subpkg/login/login'\r\n\t\t\t// \t})\r\n\t\t\t// \treturn\r\n\t\t\t// }\r\n\t\t\t// 已登录则获取数据\r\n\t\t\tthis.fetchMonthData(this.currentDate)\r\n\t\t}\r\n\t};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n\t.container {\r\n\t\tbackground-color: #fff;\r\n\t\tbackground-image: url('https://yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/h5/university_companion_bg.png');\r\n\t\tbackground-size: contain;\r\n\t\tbackground-repeat: no-repeat;\r\n\t\tpadding-top: 320rpx;\r\n\t\tmin-height: 100vh;\r\n\t\tpadding-bottom: 90rpx;\r\n\t}\r\n\r\n\t.title {\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333;\r\n\t}\r\n\r\n\t.subtitle {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #666;\r\n\t}\r\n\r\n\t.task-section {\r\n\t\tbackground-color: #fff;\r\n\t\tpadding: 20rpx;\r\n\t\tborder-radius: 40rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\t\tpadding: 46rpx 30rpx;\r\n\t}\r\n\r\n\t.task-header {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\r\n\t.tip {\r\n\t\t.tip-text {\r\n\t\t\tposition: relative;\r\n\t\t\tdisplay: inline-block;\r\n\t\t\tfont-weight: 800;\r\n\t\t\tfont-size: 30rpx;\r\n\t\t\tcolor: #060606;\r\n\t\t\tz-index: 100;\r\n\t\t}\r\n\r\n\t\t.tip-text::after {\r\n\t\t\tcontent: \"\";\r\n\t\t\tposition: absolute;\r\n\t\t\tbottom: -6rpx;\r\n\t\t\tleft: 0;\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 20rpx;\r\n\t\t\t/* 指定高度 */\r\n\t\t\tbackground-color: #DBFF9C;\r\n\t\t\t/* 底部背景颜色 */\r\n\t\t\tz-index: -1;\r\n\t\t}\r\n\t}\r\n\r\n\t.task-title {\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333;\r\n\r\n\t\ttext {\r\n\t\t\tflex: 1.5;\r\n\t\t}\r\n\t}\r\n\r\n\t.title {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: flex-start;\r\n\r\n\t\t.tag {\r\n\t\t\theight: 48rpx;\r\n\t\t\tline-height: 48rpx;\r\n\t\t\tfont-weight: 500;\r\n\t\t\tfont-size: 26rpx;\r\n\t\t\tcolor: #060606;\r\n\t\t\tmargin-right: 20rpx;\r\n\t\t}\r\n\r\n\t\t.progress {\r\n\t\t\twidth: 470rpx;\r\n\t\t\theight: 20rpx;\r\n\t\t\tbackground: #E6E6E6;\r\n\t\t\tborder-radius: 20rpx;\r\n\t\t\tmargin-right: 20rpx;\r\n\t\t\tflex: 1;\r\n\r\n\t\t\t.overlay {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\theight: 100%;\r\n\t\t\t\tborder-radius: 20rpx;\r\n\t\t\t\tbackground-repeat: repeat-x;\r\n\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.title-info {\r\n\t\t\tfont-size: 26rpx;\r\n\t\t\tcolor: #414141;\r\n\t\t}\r\n\t}\r\n\r\n\t.completion-text {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #666;\r\n\t}\r\n\r\n\t.task-list {\r\n\t\tmargin-top: 20rpx;\r\n\t}\r\n\r\n\t.task-item {\r\n\t\tposition: relative;\r\n\t\tmargin-bottom: 46rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\r\n\t\t.task-index {\r\n\t\t\tfont-weight: 800;\r\n\t\t\tfont-size: 30rpx;\r\n\t\t\tcolor: #060606;\r\n\t\t\tmargin-right: 24rpx;\r\n\t\t}\r\n\r\n\t\t.tagBox {\r\n\t\t\tposition: absolute;\r\n\t\t\ttop: -14rpx;\r\n\t\t\tleft: 60rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\r\n\t\t\t.tag {\r\n\t\t\t\twidth: 94rpx;\r\n\t\t\t\theight: 34rpx;\r\n\t\t\t\tbackground-image: url('https://yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/preset/dxt/yellow_flg.png');\r\n\t\t\t\tbackground-size: contain;\r\n\t\t\t\tbackground-repeat: no-repeat;\r\n\t\t\t\tfont-weight: 400;\r\n\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\tcolor: #FFFFFF;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tpadding-right: 12rpx;\r\n\t\t\t\tline-height: 34rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.tags {\r\n\t\t\t\tbackground-image: url('https://yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/preset/dxt/red_flag.png');\r\n\t\t\t\tbackground-size: contain;\r\n\t\t\t\tbackground-repeat: no-repeat;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\r\n\r\n\t\t.task-title {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\tbackground: #FFFFFF;\r\n\t\t\tbox-shadow: 0rpx 3rpx 6rpx 1rpx rgba(158, 158, 158, 0.16);\r\n\t\t\tborder-radius: 12rpx;\r\n\t\t\tborder: 1rpx solid #01997A;\r\n\t\t\twidth: 630rpx;\r\n\t\t\theight: 100rpx;\r\n\t\t\tpadding: 0 26rpx;\r\n\t\t\tflex: 1;\r\n\r\n\t\t\t.detail {\r\n\t\t\t\twidth: 110rpx;\r\n\t\t\t\theight: 50rpx;\r\n\t\t\t\tbackground: linear-gradient(90deg, #10C19D 0%, #16AC8E 100%);\r\n\t\t\t\tborder-radius: 16rpx;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\tcolor: #FFFFFF;\r\n\t\t\t\tline-height: 50rpx;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tmargin: 0;\r\n\r\n\t\t\t\t&:last-child {\r\n\t\t\t\t\tmargin-left: 20rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.btnBox {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: flex-end;\r\n\t\tflex: 1;\r\n\t}\r\n\r\n\t.suggestion-section {\r\n\t\tpadding: 0 30rpx;\r\n\r\n\t\t.suggestion-content {\r\n\t\t\tpadding: 30rpx;\r\n\t\t\tbackground: #F5FFFD;\r\n\t\t\tbox-shadow: 0rpx 3rpx 6rpx 1rpx rgba(136, 133, 133, 0.16);\r\n\t\t\tborder-radius: 20rpx;\r\n\t\t\tmin-height: 400rpx;\r\n\r\n\t\t\t.line {\r\n\t\t\t\theight: 70rpx;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tcolor: #4A4A4C;\r\n\t\t\t\tline-height: 70rpx;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tborder-bottom: 1rpx dashed #01997A;\r\n\t\t\t\tpadding-bottom: 10rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.imgs {\r\n\t\t\t\tmargin-top: 34rpx;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tflex-wrap: wrap;\r\n\t\t\t\talign-items: center;\r\n\r\n\t\t\t\t.img {\r\n\t\t\t\t\tmargin-top: 10rpx;\r\n\t\t\t\t\twidth: 280rpx;\r\n\t\t\t\t\theight: 280rpx;\r\n\t\t\t\t\tborder-radius: 15rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.mask {\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\theight: 100vh;\r\n\t\twidth: 100vw;\r\n\t\tbackground-color: rgba(0, 0, 0, 0.5);\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tz-index: 999;\r\n\t\tpadding: 0;\r\n\r\n\t\t.content {\r\n\t\t\tposition: relative;\r\n\t\t\twidth: 632rpx;\r\n\t\t\theight: 936rpx;\r\n\t\t\tbackground: linear-gradient(181deg, #CBF2E0 0%, #FFFFFF 35%);\r\n\t\t\tborder-radius: 32rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-direction: column;\r\n\r\n\t\t\t.content-scroll {\r\n\t\t\t\tflex: 1;\r\n\t\t\t\toverflow-y: auto;\r\n\t\t\t\tpadding: 36rpx 40rpx;\r\n\t\t\t\tpadding-bottom: 120rpx; // 为底部按钮留出空间\r\n\t\t\t\theight: calc(100% - 120rpx); // 减去底部按钮的高度\r\n\t\t\t}\r\n\r\n\t\t\t.title {\r\n\t\t\t\tfont-weight: 800;\r\n\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\tcolor: #504E4E;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\tmargin-bottom: 10rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.task-content,\r\n\t\t\t.task-content-only-one {\r\n\t\t\t\tmargin-bottom: 28rpx;\r\n\t\t\t\tfont-weight: 400;\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tcolor: #4A4A4C;\r\n\t\t\t\tline-height: 46rpx;\r\n\r\n\t\t\t\t.tip {\r\n\t\t\t\t\tmargin-bottom: 30rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.task-content-list-text {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tflex-direction: column;\r\n\t\t\t\t\talign-items: flex-start;\r\n\t\t\t\t\tjustify-content: space-between;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.task-content-only-one {\r\n\t\t\t\timage {\r\n\t\t\t\t\tmargin-top: 40rpx;\r\n\t\t\t\t\twidth: 400rpx;\r\n\t\t\t\t\theight: 400rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.task-content-image {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tpadding: 20rpx 0;\r\n\r\n\t\t\t\timage {\r\n\t\t\t\t\twidth: 390rpx;\r\n\t\t\t\t\tborder-radius: 16rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.down-load {\r\n\t\t\t\tmargin-top: 24rpx;\r\n\t\t\t\twidth: 539rpx;\r\n\t\t\t\theight: 85rpx;\r\n\t\t\t\tbackground: #FFFFFF;\r\n\t\t\t\tborder-radius: 12rpx;\r\n\t\t\t\tborder: 1rpx solid #2FC293;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: space-between;\r\n\t\t\t\tpadding: 0 18rpx;\r\n\r\n\t\t\t\t.down-load-icon {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tjustify-content: flex-start;\r\n\r\n\t\t\t\t\t.icon {\r\n\t\t\t\t\t\theight: 24rpx;\r\n\t\t\t\t\t\twidth: 24rpx;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.text {\r\n\t\t\t\t\t\tmargin-left: 4rpx;\r\n\t\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\t\tcolor: #4A4A4C;\r\n\t\t\t\t\t\tmax-width: 300rpx;\r\n\t\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\t\ttext-overflow: ellipsis;\r\n\t\t\t\t\t\twhite-space: nowrap;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.down-load-btn {\r\n\t\t\t\t\tmargin: 0;\r\n\t\t\t\t\twidth: 120rpx;\r\n\t\t\t\t\theight: 44rpx;\r\n\t\t\t\t\tbackground: linear-gradient(90deg, #10C19D 0%, #16AC8E 100%);\r\n\t\t\t\t\tborder-radius: 16rpx;\r\n\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\tcolor: #FFFFFF;\r\n\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\tline-height: 44rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.history-list {\r\n\t\t\t\theight: 100%;\r\n\t\t\t\toverflow-y: scroll;\r\n\r\n\t\t\t\t.history-item {\r\n\t\t\t\t\tmargin-bottom: 40rpx;\r\n\r\n\t\t\t\t\t.history-date {\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\tjustify-content: flex-start;\r\n\r\n\t\t\t\t\t\t.blue-block {\r\n\t\t\t\t\t\t\twidth: 8rpx;\r\n\t\t\t\t\t\t\theight: 30rpx;\r\n\t\t\t\t\t\t\tbackground: #2FC293;\r\n\t\t\t\t\t\t\tborder-radius: 12rpx 12rpx;\r\n\t\t\t\t\t\t\tmargin-right: 20rpx;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\ttext {\r\n\t\t\t\t\t\t\tfont-weight: 800;\r\n\t\t\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t\t\t\tcolor: #060606;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.history-content {\r\n\t\t\t\t\t\tmargin-top: 26rpx;\r\n\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\t\tcolor: #4A4A4C;\r\n\t\t\t\t\t\tline-height: 50rpx;\r\n\t\t\t\t\t\tmargin-bottom: 16rpx;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\timage {\r\n\t\t\t\t\t\theight: 230rpx;\r\n\t\t\t\t\t\twidth: 230rpx;\r\n\t\t\t\t\t\tborder-radius: 20rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.bottom-btn-container {\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tbottom: 0;\r\n\t\t\t\tleft: 0;\r\n\t\t\t\tright: 0;\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\theight: 120rpx;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\tbackground-color: #fff;\r\n\t\t\t\tborder-radius: 0 0 32rpx 32rpx;\r\n\r\n\t\t\t\t.bottom-btn {\r\n\t\t\t\t\twidth: 470rpx;\r\n\t\t\t\t\theight: 80rpx;\r\n\t\t\t\t\tbackground: linear-gradient(268deg, #26C8AC 0%, #19C990 100%);\r\n\t\t\t\t\tborder-radius: 40rpx;\r\n\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t\tcolor: #FFFFFF;\r\n\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\tline-height: 80rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.bottom-btn-disabled {\r\n\t\t\t\t\tbackground: #CCCCCC;\r\n\t\t\t\t\tcursor: not-allowed;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./university_companion.vue?vue&type=style&index=0&id=8e603e4a&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./university_companion.vue?vue&type=style&index=0&id=8e603e4a&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754557255557\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}