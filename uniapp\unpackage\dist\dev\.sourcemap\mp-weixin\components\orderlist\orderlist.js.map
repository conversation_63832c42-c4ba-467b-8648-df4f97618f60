{"version": 3, "sources": ["webpack:///G:/code/uniapp/daxuetonguni/uniapp/components/orderlist/orderlist.vue?566e", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/components/orderlist/orderlist.vue?bd96", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/components/orderlist/orderlist.vue?1502", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/components/orderlist/orderlist.vue?4d75", "uni-app:///components/orderlist/orderlist.vue", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/components/orderlist/orderlist.vue?6f7a", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/components/orderlist/orderlist.vue?7b7b"], "names": ["name", "props", "content", "type", "default", "required", "activeIndex", "data", "select_index", "watch", "methods", "select", "console"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;AACc;;;AAGtE;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAmmB,CAAgB,6nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCgBvnB;EACAA;EACAC;IACAC;MACAC;MACAC;MACAC;IACA;IACAC;MACAH;IACA;EACA;EACAI;IACA;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;EAAA,CACA;EACAC;IACAC;MACAC;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AC7CA;AAAA;AAAA;AAAA;AAA8oC,CAAgB,onCAAG,EAAC,C;;;;;;;;;;;ACAlqC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/orderlist/orderlist.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./orderlist.vue?vue&type=template&id=e20cffc4&\"\nvar renderjs\nimport script from \"./orderlist.vue?vue&type=script&lang=js&\"\nexport * from \"./orderlist.vue?vue&type=script&lang=js&\"\nimport style0 from \"./orderlist.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/orderlist/orderlist.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./orderlist.vue?vue&type=template&id=e20cffc4&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./orderlist.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./orderlist.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<!-- 左边侧边 -->\r\n\t<view>\r\n\t\t<view class=\"content\">\r\n\t\t\t<scroll-view style=\"width: 100%; height: 100%;\" scroll-y=\"true\">\r\n\t\t\t\t<block v-for=\"(item,index) in content\" :key=\"item\">\r\n\t\t\t\t\t<view :class=\"['text',activeIndex==index ? 'active': '']\" @tap=\"select(item,index)\">\r\n\t\t\t\t\t\t{{item.name}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</block>\r\n\t\t\t</scroll-view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tname: \"orderlist\",\r\n\t\tprops: {\r\n\t\t\tcontent: {\r\n\t\t\t\ttype: Array,\r\n\t\t\t\tdefault: [],\r\n\t\t\t\trequired: true\r\n\t\t\t},\r\n\t\t\tactiveIndex: {\r\n\t\t\t\ttype: Number\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tselect_index: 0\r\n\t\t\t};\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\t// activeIndex: function(vlo, rol) {\r\n\t\t\t// \tthis.select_index = 0\r\n\t\t\t// }\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tselect(goods, index) {\r\n\t\t\t\tconsole.log(index);\r\n\t\t\t\tthis.select_index = index\r\n\t\t\t\tthis.$emit('aid_mgs', goods, index)\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.content {\r\n\t\twidth: 179rpx;\r\n\t\theight: 63vh;\r\n\t\tpadding-bottom: 200rpx;\r\n\t\tbackground: #F6F7FB;\r\n\t\tborder-radius: 0rpx 18rpx 0rpx 0rpx;\r\n\t\tposition: fixed;\r\n\r\n\t\t.text {\r\n\t\t\twidth: 179rpx;\r\n\t\t\theight: 92rpx;\r\n\t\t\tfont-size: 24rpx;\r\n\t\t\tfont-weight: 400;\r\n\t\t\tcolor: #87888B;\r\n\t\t\tline-height: 92rpx;\r\n\t\t\ttext-align: center;\r\n\r\n\r\n\t\t}\r\n\r\n\t\t.active {\r\n\t\t\tfont-weight: bold;\r\n\t\t\tcolor: #00C2A0;\r\n\t\t\tposition: relative;\r\n\t\t\tbackground-color: #fff;\r\n\t\t\tborder-radius: 0rpx 16rpx 16rpx 0rpx;\r\n\r\n\t\t\t&::before {\r\n\t\t\t\tcontent: '';\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\twidth: 10rpx;\r\n\t\t\t\theight: 52rpx;\r\n\t\t\t\ttop: 50%;\r\n\t\t\t\tleft: 0;\r\n\t\t\t\ttransform: translateY(-50%);\r\n\t\t\t\tbackground-color: #00C2A0;\r\n\t\t\t\tborder-radius: 0rpx 16rpx 16rpx 0rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./orderlist.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./orderlist.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754557258887\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}