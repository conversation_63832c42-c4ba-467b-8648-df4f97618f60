{"version": 3, "sources": ["uni-app:///main.js", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/pages/order/order.vue?8805", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/pages/order/order.vue?9fdb", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/pages/order/order.vue?6bf3", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/pages/order/order.vue?2720", "uni-app:///pages/order/order.vue", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/pages/order/order.vue?c553", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/pages/order/order.vue?14d4", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/pages/order/order.vue?e513", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/pages/order/order.vue?981e"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "menuItems", "activeIndex", "titleList", "titleIndex", "addshow", "popshow", "activate_data", "show_specification", "counp_show", "list1", "childindex", "store_default_data", "store_infoApi_data", "orderlist", "store_id", "specification_list", "order_id", "order_type", "style", "orderprive", "shopping_trolley_list", "display", "enter", "System_height", "components_index", "scene", "seatShow", "peopleCur", "peopleCount", "people", "addAdish", "option", "activeId", "pageInfo", "total", "page", "loadMoreStatus", "contentText", "contentdown", "contentrefresh", "contentnomore", "showLocationError", "locationErrorMsg", "location", "schoolDetail", "isAdd", "pageStatus", "onShow", "showCart", "user", "type", "onLoad", "console", "uni", "onHide", "methods", "goPay", "title", "icon", "url", "addCar", "get<PERSON><PERSON><PERSON>", "coordinate", "errCode", "msg", "getlocation", "success", "setTimeout", "fail", "handleLocationError", "showAuthGuide", "content", "confirmText", "getMore", "getList", "categoryId", "limit", "campusId", "getTreeList", "changeTitle", "setActiveIndex", "goorderAll", "getUser", "getseat", "id", "res", "activate", "specification", "aid_mgs", "swpclick", "store_defaultApi", "shop", "store_collectionApi", "comtent", "store_infoApi", "shipment", "selective_specification", "store_itemPriceApi", "str", "goods_id", "spu_id", "add_joinCar", "count", "order_carListApi", "token", "register", "router_close", "routergo", "routergos", "closepage", "refresherpulling"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACa;AACyB;;;AAG1F;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACxBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,4KAEN;AACP,KAAK;AACL;AACA,aAAa,qRAEN;AACP,KAAK;AACL;AACA,aAAa,8LAEN;AACP,KAAK;AACL;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,aAAa,8LAEN;AACP,KAAK;AACL;AACA,aAAa,kUAEN;AACP,KAAK;AACL;AACA,aAAa,sKAEN;AACP,KAAK;AACL;AACA,aAAa,4NAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACzFA;AAAA;AAAA;AAAA;AAA+lB,CAAgB,ynBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACwHnnB;AAGA;AAgBA;AAGA;AAGA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAGA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;MACA;MACAC;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;QACAC;MACA;MACAC;MACAC;MACAC;IACA;EAEA;EACAC;IACA;IACAC;IACA;MACA;IACA;IACA;MACAC;MACA;MACA;MACA;MACA;IACA;IAGA;MACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAGA;IACA;EACA;EACAN;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACA;MACA;MACA;IACA;IACA;EAEA;EACAO;IACA;EACA;EACAC;IACA;IACAC;MACA;QACA;QACAH;UACAI;UACAC;QACA;QACA;MACA;MACAL;QACAM;MACA;MACAN;IACA;IACAO;MACA;IACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAT;gBAAA;gBAAA,OAKA;kBACAU;kBACA;gBAEA;cAAA;gBAAA;gBAPAC;gBACAhE;gBACAiE;gBAMA;kBACA;kBACA;kBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAGA;IACA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACA;gBACAZ;kBACAH;kBACAgB;oBACA;oBACAd;oBACA;oBACAe;sBACA;oBACA;kBAEA;kBACAC;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MACA;;MAEA;MACA;QACA;UACA;UACA;QAEA;UACA;UACA;UACA;UACA;QAEA;UACA;UACA;QAEA;UACA;MAAA;IAEA;IAEA;IACAC;MAAA;MACAjB;QACAI;QACAc;QACAC;QACAN;UACA;YACA;YACAb;cACAa;gBACAd;;gBAEA;gBACA;kBACA;kBACA;kBACA;gBACA;kBACA;kBACA;kBACA;gBACA;cACA;cACAgB;gBACAhB;gBACA;gBACA;cACA;YACA;UACA;QACA;MACA;IACA;IACAqB;MACA;QACA;QACA;MACA;MACA;MACA;IACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAKA;kBACAC;kBACAxC;kBACAyC;kBACA;kBACAC;gBACA;cAAA;gBAAA;gBATA9E;gBACAgE;gBACAC;gBAQA;kBACA;kBACA;kBACA;oBACA;kBACA;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAc;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAKA;cAAA;gBAAA;gBAHA/E;gBACAgE;gBACAC;gBAEA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;oBACA;kBACA;gBAEA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAe;MACA;MACA;MACA;MACA;MACA;QACA;QACA;MACA;QACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA,4GACA;MACA;MACA;QACA;QACA;QACA;QACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;QACA;MAEA;IACA;IACAC;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBAAAjC;gBACA;kBACAI;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;IACA8B;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACA;kBACAC;gBACA;cAAA;gBAFAC;gBAGA;kBACA;kBACA;kBACAhC;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAiC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBACA;kBACA;gBACA;kBACA;gBACA;gBACAjC;gBACA;gBACA;gBACA;gBAAA;gBAAA,OACA;cAAA;gBAAAtD;gBACAsD;kBACAI;kBACAC;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA6B;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACA;gBACAnC;gBACAe;kBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAqB;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MACApC;QACAM;MACA;IACA;IAEA+B;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBAAA;gBAAA,OACA;kBACAP;gBACA;cAAA;gBAFArF;gBAGA;gBACA;kBACA;gBACA;gBACA;gBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA6F;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACAvC;kBAAA;kBAAA;gBAAA;gBACA;gBACAA;kBACAI;kBACAC;gBACA;gBAAA;cAAA;gBAAA,KAGAmC;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;kBACA/E;gBACA;cAAA;gBAFAf;gBAGA;kBACA;kBACA;kBACA;kBACA;gBAAA,CACA;kBACA;kBACA;kBACA;kBACA;gBAAA;gBACA;gBAAA;cAAA;gBAAA;gBAAA,OAEA;kBACAe;gBACA;cAAA;gBAFAf;gBAGA;kBACA;kBACA;kBACA;kBACA;gBAAA,CACA;kBACA;kBACA;kBACA;kBACA;gBAAA;cACA;gBAEA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACA+F;MAAA;MAAA;QAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACA;kBACAhF;kBACAiF;gBACA;cAAA;gBAHAhG;gBAIA;gBACA;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEAiG;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAEA;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBACA;kBACAA;gBACA;gBACAA;gBAAA;gBAAA,OACA;kBACAC;kBACAC;gBACA;cAAA;gBAHArG;gBAIA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;IACAsG;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACAhD;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAGA6C;gBACA;kBACAA;gBACA;gBACAA;gBAAA;gBAAA,OACA;kBACApF;kBACAqF;kBACAC;kBACAE;kBACArF;gBAEA;cAAA;gBAPAlB;gBAQA;kBACAsD;oBACAI;oBACAC;kBACA;kBACA;kBACA;kBACA;gBACA;kBACAL;oBACAI;oBACAC;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;IACA6C;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAAzG;gBACA;kBACA;gBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACA;IACA0G;MACA;IACA;IACAC;MACArD;QACAM;MACA;IACA;IACAgD;MACAtD;QACAM;MACA;IACA;IACAiD;MACA;MACA;MACA;QACAvD;UACAM;QACA;MACA;QACA;MACA;IACA;IACA;IACAkD;MACA;IACA;IACAC,+CAEA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC1tBA;AAAA;AAAA;AAAA;AAAi3B,CAAgB,k3BAAG,EAAC,C;;;;;;;;;;;ACAr4B;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAAkqC,CAAgB,woCAAG,EAAC,C;;;;;;;;;;;ACAtrC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/order/order.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/order/order.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./order.vue?vue&type=template&id=127632e4&scoped=true&\"\nvar renderjs\nimport script from \"./order.vue?vue&type=script&lang=js&\"\nexport * from \"./order.vue?vue&type=script&lang=js&\"\nimport style0 from \"./order.vue?vue&type=style&index=0&lang=css&\"\nimport style1 from \"./order.vue?vue&type=style&index=1&id=127632e4&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"127632e4\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/order/order.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./order.vue?vue&type=template&id=127632e4&scoped=true&\"", "var components\ntry {\n  components = {\n    height: function () {\n      return import(\n        /* webpackChunkName: \"components/height/height\" */ \"@/components/height/height.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    orderlist: function () {\n      return import(\n        /* webpackChunkName: \"components/orderlist/orderlist\" */ \"@/components/orderlist/orderlist.vue\"\n      )\n    },\n    uEmpty: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-empty/u-empty\" */ \"uview-ui/components/u-empty/u-empty.vue\"\n      )\n    },\n    ordercard: function () {\n      return import(\n        /* webpackChunkName: \"components/ordercard/ordercard\" */ \"@/components/ordercard/ordercard.vue\"\n      )\n    },\n    uniLoadMore: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/@dcloudio/uni-ui/lib/uni-load-more/uni-load-more\" */ \"@dcloudio/uni-ui/lib/uni-load-more/uni-load-more.vue\"\n      )\n    },\n    close: function () {\n      return import(\n        /* webpackChunkName: \"components/close/close\" */ \"@/components/close/close.vue\"\n      )\n    },\n    login: function () {\n      return import(\n        /* webpackChunkName: \"components/login/login\" */ \"@/components/login/login.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.titleList.length\n  var g1 = _vm.orderlist.length\n  var g2 = _vm.orderlist.length\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.enter = true\n    }\n    _vm.e1 = function ($event) {\n      _vm.counp_show = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./order.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./order.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\r\n\t\t<height :hg='System_height'></height>\r\n\t\t<view v-if=\"showLocationError\" class=\"location-error-toast\">\r\n\t\t\t<text class=\"error-text\">定位失败，请重新开启定位</text>\r\n\t\t\t<button class=\"retry-btn\" @click=\"getlocation\">去开启</button>\r\n\t\t</view>\r\n\t\t<view class=\"search\">\r\n\t\t\t<view class=\"search_left\">\r\n\t\t\t\t<image src=\"@/static/orderLogo.png\" mode=\"\"></image>\r\n\t\t\t</view>\r\n\t\t\t<!-- <view style=\"display: flex; padding-right: 200rpx;\">\r\n\t\t\t\t<view class=\"search_right\" @tap=\"store_collectionApi(store_default_data.is_collection)\">\r\n\t\t\t\t\t<image src=\"../../static/Project_drawing 40.png\" v-if=\"store_default_data.is_collection\" mode=\"\">\r\n\t\t\t\t\t</image>\r\n\t\t\t\t\t<image src=\"../../static/Project_drawing 39.png\" v-else mode=\"\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"search_right\"\r\n\t\t\t\t\t@tap=\"routerTo('/pages/order_all/ordersearch/ordersearch?id='+store_default_data.id)\">\r\n\t\t\t\t\t<image src=\"../../static/Project_drawing 34.png\" mode=\"\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t</view> -->\r\n\t\t</view>\r\n\t\t<view class=\"hade_location\">\r\n\t\t\t<view class=\"hade_location_left\" @tap=\"goorderAll()\">\r\n\t\t\t\t<view class=\"hade_location_left_top\">\r\n\t\t\t\t\t<text>{{showLocationError ?'定位失败':schoolDetail.name}}</text>\r\n\t\t\t\t\t<!-- <text>{{schoolDetail.name}}</text> -->\r\n\r\n\t\t\t\t\t<view class=\"schoolName\" v-if=\"schoolDetail.isPremier\">\r\n\t\t\t\t\t\t<image src=\"@/static/king.png\" mode=\"\"></image>\r\n\t\t\t\t\t\t<text>旗舰校区</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<u-icon name=\"arrow-right\" color='#313131' size=\"24rpx\"></u-icon>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"hade_location_left_down\">\r\n\t\t\t\t\t{{schoolDetail.addr||schoolDetail.selectedPlace}}\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"scrollBox\">\r\n\t\t\t<scroll-view class=\"menu\" scroll-x>\r\n\t\t\t\t<!-- 修改后的菜单项 -->\r\n\t\t\t\t<view :class=\"['menu-item',activeIndex === index ? 'active' : '']\" v-for=\"(item, index) in menuItems\"\r\n\t\t\t\t\t:key=\"item.id\" @tap=\"setActiveIndex(index)\">\r\n\t\t\t\t\t{{ item.name }}\r\n\t\t\t\t</view>\r\n\t\t\t</scroll-view>\r\n\t\t</view>\r\n\t\t<view class=\"felx\">\r\n\t\t\t<orderlist @aid_mgs='aid_mgs' :activeIndex='components_index' :content='store_infoApi_data'></orderlist>\r\n\t\t\t<view class=\"felx_right_box\">\r\n\t\t\t\t<scroll-view scroll-y=\"true\" style=\"height: 100%;\">\r\n\t\t\t\t\t<view class=\"right_title\">\r\n\t\t\t\t\t\t<view :class=\"['title',titleIndex=='-1' ? 'title_active' : '']\" @click=\"changeTitle('-1')\">全部\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<template v-if=\"titleList.length\">\r\n\t\t\t\t\t\t\t<view :class=\"['title',titleIndex==index ? 'title_active' : '']\"\r\n\t\t\t\t\t\t\t\tv-for=\"(item,index) in titleList\" :key=\"index\" @click=\"changeTitle(index,item.id)\">\r\n\t\t\t\t\t\t\t\t{{item.name}}\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</template>\r\n\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- \t:refresher-enabled='true'\r\n\t\t\t\t\t\t@refresherpulling='refresherpulling' -->\r\n\t\t\t\t\t<view class=\"felx_right_box_conten\">\r\n\t\t\t\t\t\t<!-- <view class=\"felx_right_box_conten_img\">\r\n\t\t\t\t\t\t\t<u-swiper :list=\"list1\" :height='251' @click=\"swpclick(item.url)\"></u-swiper>\r\n\t\t\t\t\t\t</view> -->\r\n\t\t\t\t\t\t<!-- <view class=\"felx_right_box_conten_title\">\r\n\t\t\t\t\t\t\t{{store_infoApi_data[childindex].name}}\r\n\t\t\t\t\t\t</view> -->\r\n\t\t\t\t\t\t<view style=\"margin-top: 150rpx;\" v-if=\"orderlist.length<1\">\r\n\t\t\t\t\t\t\t<u-empty mode=\"data\" :iconSize='150' :textSize='24' text='暂无此类产品' icon=\"\"></u-empty>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<ordercard :order_id='order_id' @specification=\"specification\" :content='orderlist'\r\n\t\t\t\t\t\t\t:store_id='store_default_data.id' @order='order_carListApi' :activate_data='activate_data'\r\n\t\t\t\t\t\t\t@login='enter=true' @addCar=\"addCar\">\r\n\t\t\t\t\t\t</ordercard>\r\n\t\t\t\t\t\t<!-- <ordercard2 :order_id='order_id' @specification=\"specification\" :content='orderlist'\r\n\t\t\t\t\t\t\t:store_id='store_default_data.id' @order='order_carListApi' :activate_data='activate_data'\r\n\t\t\t\t\t\t\t@login='enter=true'>\r\n\t\t\t\t\t\t</ordercard2> -->\r\n\t\t\t\t\t\t<uni-load-more v-if=\"orderlist.length\" @clickLoadMore=\"getMore\" :contentText='contentText'\r\n\t\t\t\t\t\t\t:status=\"loadMoreStatus\"></uni-load-more>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<height :hg='70'></height>\r\n\t\t\t\t</scroll-view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<close v-if=\"!user.user\" :shopping_trolley_list='shopping_trolley_list' @update='order_carListApi'\r\n\t\t\t@register='register' :isAdd=\"isAdd\" :page-status=\"pageStatus\" @goPay=\"goPay\" :showCart=\"showCart\">\r\n\t\t</close>\r\n\r\n\r\n\t\t<login :show=\"enter\" @closepage='closepage'></login>\r\n\t\t<movable-area class=\"movableArea\">\r\n\t\t\t<movable-view class=\"movableView\" direction=\"all\" x=\"600rpx\" y=\"800rpx\">\r\n\t\t\t\t<view class=\"coupn\" v-if=\"counp_show\">\r\n\t\t\t\t\t<view class=\"coupn_icon\" @tap=\"counp_show=false\">\r\n\t\t\t\t\t\t<u-icon name=\"close-circle-fill\" color=\"#0C0B0B\" size=\"28\"></u-icon>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"coupn_img\" @tap=\"routergos('/pages/me_all/coupon_collection/coupon_collection')\">\r\n\t\t\t\t\t\t<image src=\"../../static/Project_drawing 38.png\" mode=\"\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"coupn_title\" @tap=\"routergos('/pages/me_all/coupon_collection/coupon_collection')\">\r\n\t\t\t\t\t\t<view class=\"coupn_title_text\">\r\n\t\t\t\t\t\t\t领券中心\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<u-icon name=\"arrow-right\" color=\"#68150A \" size=\"22\"></u-icon>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</movable-view>\r\n\t\t</movable-area>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport\r\n\tLocation\r\n\tfrom \"@/utils/wxApi.js\"\r\n\timport {\r\n\t\tstore_default,\r\n\t\tstore_collection,\r\n\t\tstore_cancelColl,\r\n\t\tstore_info,\r\n\t\tstore_itemPrice,\r\n\t\tstore_item,\r\n\t\tstore_goodsSearch,\r\n\t\torder_joinCar,\r\n\t\torder_carList,\r\n\t\torder_empty,\r\n\t\tseat,\r\n\t\ttreeList,\r\n\t\tgetProductList,\r\n\t\tgetNearCampus\r\n\t} from \"@/api/comm.js\"\r\n\timport {\r\n\t\tforEach\r\n\t} from \"lodash\";\r\n\timport {\r\n\t\tcellphone\r\n\t} from \"@/utils/type_height.js\"\r\n\timport {\r\n\t\tuserInfo\r\n\t} from \"@/api/public.js\"\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tmenuItems: [],\r\n\t\t\t\tactiveIndex: 0,\r\n\t\t\t\ttitleList: [],\r\n\t\t\t\ttitleIndex: '-1',\r\n\t\t\t\taddshow: true,\r\n\t\t\t\tpopshow: false,\r\n\t\t\t\t// 胶囊开关\r\n\t\t\t\tactivate_data: 2,\r\n\t\t\t\t// 规格参数\r\n\t\t\t\tshow_specification: false,\r\n\t\t\t\t// 领卷\r\n\t\t\t\tcounp_show: true,\r\n\t\t\t\tlist1: [],\r\n\t\t\t\tchildindex: 0, //侧边栏子组件传过来的索引\r\n\t\t\t\tstore_default_data: {}, //默认门店\r\n\t\t\t\tstore_infoApi_data: [], //分类列表\r\n\t\t\t\torderlist: [], //此分类下的商品\r\n\t\t\t\tstore_id: '', //选择门店后使用选择的门店\r\n\t\t\t\tspecification_list: [], //规格参数\r\n\t\t\t\torder_id: '', //当前规格商品id\r\n\t\t\t\torder_type: null, //判断多规格还是单规格\r\n\t\t\t\tstyle: [], //规格动态样式\r\n\t\t\t\torderprive: 0, //价格\r\n\t\t\t\tshopping_trolley_list: {}, //购物车列表\r\n\t\t\t\tdisplay: false, //页面数据未加载前隐藏\r\n\t\t\t\tenter: false, //判断是否登录登录\r\n\t\t\t\tSystem_height: cellphone(), //系统高度\r\n\t\t\t\tcomponents_index: 0, //刷新组件索引\r\n\t\t\t\tscene: null,\r\n\t\t\t\tseatShow: true,\r\n\t\t\t\tpeopleCur: 0,\r\n\t\t\t\tpeopleCount: 1,\r\n\t\t\t\tpeople: '',\r\n\t\t\t\taddAdish: false,\r\n\t\t\t\toption: null,\r\n\t\t\t\tactiveId: '',\r\n\t\t\t\tpageInfo: {\r\n\t\t\t\t\ttotal: 0,\r\n\t\t\t\t\tpage: 1\r\n\t\t\t\t},\r\n\t\t\t\tloadMoreStatus: 'more',\r\n\t\t\t\tcontentText: {\r\n\t\t\t\t\tcontentdown: \"点击加载更多\",\r\n\t\t\t\t\tcontentrefresh: \"正在加载...\",\r\n\t\t\t\t\tcontentnomore: \"没有更多数据了\"\r\n\t\t\t\t},\r\n\t\t\t\tshowLocationError: false, // 控制悬浮提示显示\r\n\t\t\t\tlocationErrorMsg: \"\", // 错误信息\r\n\t\t\t\tlocation: {}, //存储定位信息\r\n\t\t\t\tschoolDetail: {}, //校区信息\r\n\t\t\t\tisAdd: true,\r\n\t\t\t\tpageStatus: {\r\n\t\t\t\t\tonShow: false\r\n\t\t\t\t},\r\n\t\t\t\tshowCart: false,\r\n\t\t\t\tuser: {},\r\n\t\t\t\ttype: 0,\r\n\t\t\t};\r\n\r\n\t\t},\r\n\t\tonLoad(e) {\r\n\t\t\tthis.orderlist = []\r\n\t\t\tconsole.log('e', e)\r\n\t\t\tif (!e.query) {\r\n\t\t\t\tthis.getTreeList()\r\n\t\t\t}\r\n\t\t\tif (e.query.item) {\r\n\t\t\t\tuni.removeStorageSync('cartItems')\r\n\t\t\t\tthis.type = 1\r\n\t\t\t\tthis.schoolDetail = JSON.parse(e.query.item)\r\n\t\t\t\t// this.getList()\r\n\t\t\t\tthis.getTreeList()\r\n\t\t\t}\r\n\r\n\r\n\t\t\tif (e.showCart === 'true') {\r\n\t\t\t\tthis.showCart = true;\r\n\t\t\t}\r\n\t\t\t// if (e.scene) {\r\n\t\t\t// \tlet option = uni.getStorageSync('option')\r\n\t\t\t// \tif (option !== 3) {\r\n\t\t\t// \t\torder_empty()\r\n\t\t\t// \t}\r\n\t\t\t// \tlet bb = e.scene.split('_')\r\n\t\t\t// \tlet cc = bb[1]\r\n\t\t\t// \tif (cc == '1' || cc == 1) {\r\n\t\t\t// \t\tuni.setStorageSync('firstPay', 1)\r\n\t\t\t// \t} else {\r\n\t\t\t// \t\tuni.setStorageSync('firstPay', 2)\r\n\t\t\t// \t}\r\n\t\t\t// \tuni.setStorageSync('option', 3)\r\n\t\t\t// \tuni.setStorageSync('scene', bb[0])\r\n\t\t\t// \tthis.getseat(bb[0])\r\n\t\t\t// }\r\n\r\n\r\n\t\t\t// this.store_defaultApi(1) //默认门店\r\n\t\t\t// this.getAddress()\r\n\t\t},\r\n\t\tonShow() {\r\n\t\t\tthis.user = uni.getStorageSync('user')\r\n\t\t\tthis.pageStatus.onShow = true\r\n\t\t\t// this.store_defaultApi() //默认门店\r\n\t\t\t// let user = uni.getStorageSync('user')\r\n\t\t\t// let option = uni.getStorageSync('option')\r\n\t\t\t// let scene = uni.getStorageSync('scene')\r\n\t\t\t// let firstPay = uni.getStorageSync('firstPay')\r\n\t\t\t// this.option = option\r\n\t\t\t// if (user) {\r\n\t\t\t// \tthis.getUser()\r\n\t\t\t// }\r\n\t\t\t// if (option == 3) {\r\n\t\t\t// \tthis.seatShow = false\r\n\t\t\t// \tthis.getseat(scene)\r\n\t\t\t// } else {\r\n\t\t\t// \tif (scene) {\r\n\t\t\t// \t\tuni.removeStorageSync('scene')\r\n\t\t\t// \t}\r\n\t\t\t// \tthis.seatShow = true\r\n\t\t\t// }\r\n\r\n\t\t\t// this.enter = false\r\n\t\t\t// this.order_carListApi() //购物车列表\r\n\t\t\t// if (option == 2) {\r\n\t\t\t// \tthis.activate_data = 2\r\n\t\t\t// } else {\r\n\t\t\t// \tthis.activate_data = 1\r\n\t\t\t// }\r\n\t\t\tthis.orderlist = []\r\n\t\t\tif (!this.type) {\r\n\t\t\t\tthis.$forceUpdate()\r\n\t\t\t\tthis.getAddress()\r\n\t\t\t\tthis.getlocation()\r\n\t\t\t}\r\n\t\t\t//模糊定位\r\n\r\n\t\t},\r\n\t\tonHide() {\r\n\t\t\tthis.pageStatus.onShow = false\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 去结算\r\n\t\t\tgoPay() {\r\n\t\t\t\tif (!uni.getStorageSync('user')) {\r\n\t\t\t\t\tthis.enter = true\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '未登录',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: `/pages/order_all/affirm_order/affirm_order`\r\n\t\t\t\t})\r\n\t\t\t\tuni.setStorageSync('campusId', this.schoolDetail.id)\r\n\t\t\t},\r\n\t\t\taddCar() {\r\n\t\t\t\tthis.isAdd = !this.isAdd\r\n\t\t\t},\r\n\t\t\t// 根据定位获取校区\r\n\t\t\tasync getAddress() {\r\n\t\t\t\tconsole.log([this.location.longitude, this.location.latitude].join(','))\r\n\t\t\t\tconst {\r\n\t\t\t\t\terrCode,\r\n\t\t\t\t\tdata,\r\n\t\t\t\t\tmsg\r\n\t\t\t\t} = await getNearCampus({\r\n\t\t\t\t\tcoordinate: [this.location.longitude, this.location.latitude].join(',')\r\n\t\t\t\t\t// coordinate: ['117.2832', '31.9386'].join(',')\r\n\r\n\t\t\t\t})\r\n\t\t\t\tif (errCode == 0) {\r\n\t\t\t\t\tthis.orderlist = []\r\n\t\t\t\t\tthis.showLocationError = false\r\n\t\t\t\t\tthis.schoolDetail = data\r\n\t\t\t\t\tthis.getList()\r\n\t\t\t\t}\r\n\r\n\r\n\t\t\t},\r\n\t\t\t// 获取定位权限\r\n\t\t\tasync getlocation() {\r\n\t\t\t\tthis.orderlist = []\r\n\t\t\t\tthis.locationErrorMsg = \"\";\r\n\t\t\t\tuni.getLocation({\r\n\t\t\t\t\ttype: 'gcj02',\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tthis.location = res;\r\n\t\t\t\t\t\tconsole.log('location', this.location)\r\n\t\t\t\t\t\tthis.showLocationError = false;\r\n\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\tthis.getAddress()\r\n\t\t\t\t\t\t}, 2000)\r\n\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\tthis.handleLocationError(err);\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\r\n\t\t\t// 处理定位错误\r\n\t\t\thandleLocationError(err) {\r\n\t\t\t\tthis.showLocationError = true\r\n\r\n\t\t\t\t// 解析错误类型\r\n\t\t\t\tswitch (true) {\r\n\t\t\t\t\tcase /cancel/.test(err.errMsg):\r\n\t\t\t\t\t\tthis.locationErrorMsg = \"您已取消位置选择\"\r\n\t\t\t\t\t\tbreak;\r\n\r\n\t\t\t\t\tcase /auth deny|permission/.test(err.errMsg):\r\n\t\t\t\t\t\tthis.locationErrorMsg = \"定位权限被拒绝\"\r\n\t\t\t\t\t\t// 引导用户开启权限\r\n\t\t\t\t\t\tthis.showAuthGuide()\r\n\t\t\t\t\t\tbreak;\r\n\r\n\t\t\t\t\tcase /timeout/.test(err.errMsg):\r\n\t\t\t\t\t\tthis.locationErrorMsg = \"定位超时，请重试\"\r\n\t\t\t\t\t\tbreak;\r\n\r\n\t\t\t\t\tdefault:\r\n\t\t\t\t\t\tthis.locationErrorMsg = \"定位服务不可用\"\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 显示权限引导\r\n\t\t\tshowAuthGuide() {\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: '定位权限未开启',\r\n\t\t\t\t\tcontent: '需要您的位置权限才能获取位置信息，是否前往设置开启？',\r\n\t\t\t\t\tconfirmText: '去设置',\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t// 打开系统设置\r\n\t\t\t\t\t\t\tuni.openSetting({\r\n\t\t\t\t\t\t\t\tsuccess: (settingRes) => {\r\n\t\t\t\t\t\t\t\t\tconsole.log('设置页面打开成功', settingRes.authSetting);\r\n\r\n\t\t\t\t\t\t\t\t\t// 检查用户是否开启了定位权限\r\n\t\t\t\t\t\t\t\t\tif (settingRes.authSetting['scope.userLocation']) {\r\n\t\t\t\t\t\t\t\t\t\t// 权限已开启，立即获取位置\r\n\t\t\t\t\t\t\t\t\t\tthis.orderlist = []\r\n\t\t\t\t\t\t\t\t\t\tthis.getlocation();\r\n\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\t// 用户没有开启定位权限\r\n\t\t\t\t\t\t\t\t\t\tthis.showLocationError = true;\r\n\t\t\t\t\t\t\t\t\t\tthis.locationErrorMsg = \"您仍未开启定位权限\";\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\t\t\t\tconsole.error('打开设置页面失败', err);\r\n\t\t\t\t\t\t\t\t\tthis.showLocationError = true;\r\n\t\t\t\t\t\t\t\t\tthis.locationErrorMsg = \"无法打开设置页面\";\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgetMore() {\r\n\t\t\t\tif (this.orderlist.length >= this.pageInfo.total) {\r\n\t\t\t\t\tthis.loadMoreStatus = \"noMore\"\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tthis.pageInfo.page += 1\r\n\t\t\t\tthis.getList()\r\n\t\t\t},\r\n\t\t\t// 获取产品列表\r\n\t\t\tasync getList() {\r\n\t\t\t\tconst {\r\n\t\t\t\t\tdata,\r\n\t\t\t\t\terrCode,\r\n\t\t\t\t\tmsg\r\n\t\t\t\t} = await getProductList({\r\n\t\t\t\t\tcategoryId: this.activeId,\r\n\t\t\t\t\tpage: this.pageInfo.page,\r\n\t\t\t\t\tlimit: 10,\r\n\t\t\t\t\t// campusId: this.schoolDetail.id || ''\r\n\t\t\t\t\tcampusId: this.schoolDetail.id,\r\n\t\t\t\t})\r\n\t\t\t\tif (errCode == 0) {\r\n\t\t\t\t\tthis.orderlist = [...this.orderlist, ...data.data]\r\n\t\t\t\t\tthis.pageInfo.total = data.total\r\n\t\t\t\t\tif (this.orderlist.length >= this.pageInfo.total) {\r\n\t\t\t\t\t\tthis.loadMoreStatus = \"noMore\"\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.loadMoreStatus = \"more\"\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tasync getTreeList() {\r\n\t\t\t\tconst {\r\n\t\t\t\t\tdata,\r\n\t\t\t\t\terrCode,\r\n\t\t\t\t\tmsg\r\n\t\t\t\t} = await treeList()\r\n\t\t\t\tif (errCode == 0) {\r\n\t\t\t\t\tthis.menuItems = data\r\n\t\t\t\t\tthis.store_infoApi_data = this.menuItems[0].children\r\n\t\t\t\t\tthis.titleList = this.store_infoApi_data[0].children\r\n\t\t\t\t\tthis.activeId = this.store_infoApi_data[0].id\r\n\t\t\t\t\tthis.orderlist = []\r\n\t\t\t\t\tif (this.type == 1) {\r\n\t\t\t\t\t\tthis.getList()\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tchangeTitle(index, id) {\r\n\t\t\t\tthis.titleIndex = index\r\n\t\t\t\tthis.pageInfo.page = 1\r\n\t\t\t\tthis.pageInfo.total = 0\r\n\t\t\t\tthis.orderlist = []\r\n\t\t\t\tif (index == -1) {\r\n\t\t\t\t\tthis.activeId = this.this.store_infoApi_data[this.childindex].id\r\n\t\t\t\t\tthis.getList()\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.activeId = id\r\n\t\t\t\t\tthis.getList()\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tsetActiveIndex(index) {\r\n\t\t\t\tthis.activeIndex = index\r\n\t\t\t\tthis.store_infoApi_data = this.menuItems[index].children || []\r\n\t\t\t\tthis.components_index = 0\r\n\t\t\t\tthis.titleList = this.store_infoApi_data.length ? this.store_infoApi_data[this.components_index].children :\r\n\t\t\t\t\t[]\r\n\t\t\t\tthis.titleIndex = '-1'\r\n\t\t\t\tif (this.store_infoApi_data.length) {\r\n\t\t\t\t\tthis.activeId = this.store_infoApi_data[0].id\r\n\t\t\t\t\tthis.pageInfo.page = 1\r\n\t\t\t\t\tthis.total = 0\r\n\t\t\t\t\tthis.orderlist = []\r\n\t\t\t\t\tthis.getList()\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tgoorderAll() {\r\n\t\t\t\tif (this.showLocationError) return\r\n\t\t\t\tlet option = uni.getStorageSync('option')\r\n\t\t\t\tif (option !== 3) {\r\n\t\t\t\t\tthis.routergo('/pages/order_all/selectstore/selectstore')\r\n\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tasync getUser() {\r\n\t\t\t\tlet user = await userInfo()\r\n\t\t\t\tif (user.code == 1) {\r\n\t\t\t\t\tuni.setStorageSync('user', user.data)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 获取桌号信息\r\n\t\t\tasync getseat(id) {\r\n\t\t\t\tlet res = await seat({\r\n\t\t\t\t\tid\r\n\t\t\t\t})\r\n\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\tthis.scene = res.data\r\n\t\t\t\t\tthis.seatShow = false\r\n\t\t\t\t\tuni.setStorageSync('shop', res.data.store_id)\r\n\t\t\t\t\tthis.store_defaultApi() //默认门店\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tasync activate(index) {\r\n\t\t\t\tif (this.components_index == index) return\r\n\t\t\t\tif (index == 1) {\r\n\t\t\t\t\tthis.components_index = 1\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.components_index = 2\r\n\t\t\t\t}\r\n\t\t\t\tuni.setStorageSync('option', index)\r\n\t\t\t\tthis.activate_data = index\r\n\t\t\t\tthis.store_defaultApi()\r\n\t\t\t\t// 清空购物车\r\n\t\t\t\tlet data = await order_empty()\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: data.msg,\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t})\r\n\t\t\t\tthis.order_carListApi()\r\n\t\t\t},\r\n\t\t\tasync specification(e) {\r\n\t\t\t\tthis.order_id = e.e\r\n\t\t\t\tthis.order_type = e.type\r\n\t\t\t\tconsole.log('eeee', this.order_type);\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tthis.$refs.specification.specification()\r\n\t\t\t\t\tthis.show_specification = true\r\n\t\t\t\t}, 500)\r\n\t\t\t},\r\n\t\t\taid_mgs(e, index) {\r\n\t\t\t\tthis.childindex = index\r\n\t\t\t\tthis.components_index = index\r\n\t\t\t\tthis.titleList = this.store_infoApi_data[index].children || []\r\n\t\t\t\tthis.titleIndex = '-1'\r\n\t\t\t\t// this.orderlist = []\r\n\t\t\t\t// this.orderlist.push(...e)\r\n\t\t\t\tthis.pageInfo.page = 1\r\n\t\t\t\tthis.pageInfo.total = 0\r\n\t\t\t\tthis.activeId = e.id\r\n\t\t\t\tthis.orderlist = []\r\n\t\t\t\tthis.getList()\r\n\t\t\t},\r\n\t\t\tswpclick(url) {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: url\r\n\t\t\t\t})\r\n\t\t\t},\r\n\r\n\t\t\tasync store_defaultApi(e) {\r\n\t\t\t\tlet shop = uni.getStorageSync('shop')\r\n\t\t\t\tlet data = await store_default({\r\n\t\t\t\t\tid: shop || ''\r\n\t\t\t\t})\r\n\t\t\t\tthis.store_default_data = data.data\r\n\t\t\t\tif (data.data.takeaway_switch == 1 && data.data.delivery_switch !== 1) {\r\n\t\t\t\t\tthis.activate_data = 2\r\n\t\t\t\t}\r\n\t\t\t\tthis.list1 = data.data.images\r\n\t\t\t\tif (e == 1) {\r\n\t\t\t\t\tthis.store_infoApi() //门店下的分类\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tasync store_collectionApi(comtent) {\r\n\t\t\t\tif (!uni.getStorageSync('userinfo')) {\r\n\t\t\t\t\tthis.enter = true\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '未登录',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t} else {\r\n\t\t\t\t\tif (comtent) {\r\n\t\t\t\t\t\tlet data = await store_cancelColl({\r\n\t\t\t\t\t\t\tstore_id: this.store_default_data.id\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tif (data == 1) {\r\n\t\t\t\t\t\t\t// uni.showToast({\r\n\t\t\t\t\t\t\t// \ttitle: data.msg,\r\n\t\t\t\t\t\t\t// \ticon: 'success'\r\n\t\t\t\t\t\t\t// })\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t// uni.showToast({\r\n\t\t\t\t\t\t\t// \ttitle: data.msg,\r\n\t\t\t\t\t\t\t// \ticon: 'error'\r\n\t\t\t\t\t\t\t// })\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tlet data = await store_collection({\r\n\t\t\t\t\t\t\tstore_id: this.store_default_data.id\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tif (data == 1) {\r\n\t\t\t\t\t\t\t// uni.showToast({\r\n\t\t\t\t\t\t\t// \ttitle: data.msg,\r\n\t\t\t\t\t\t\t// \ticon: 'success'\r\n\t\t\t\t\t\t\t// })\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t// uni.showToast({\r\n\t\t\t\t\t\t\t// \ttitle: data.msg,\r\n\t\t\t\t\t\t\t// \ticon: 'error'\r\n\t\t\t\t\t\t\t// })\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.store_defaultApi()\r\n\t\t\t\t\tthis.order_carListApi()\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tasync store_infoApi() {\r\n\t\t\t\tlet data = await store_info({\r\n\t\t\t\t\tstore_id: this.store_default_data.id,\r\n\t\t\t\t\tshipment: this.activate_data || 2\r\n\t\t\t\t})\r\n\t\t\t\tthis.store_infoApi_data = []\r\n\t\t\t\tthis.store_infoApi_data.push(...data.data)\r\n\t\t\t\tthis.orderlist = data.data[0].goods\r\n\t\t\t\tthis.display = true\r\n\t\t\t},\r\n\r\n\t\t\tasync selective_specification(index, id, name) {\r\n\r\n\t\t\t\tthis.$set(this.style[index], 'id', id)\r\n\t\t\t\tthis.$set(this.style[index], 'name', name)\r\n\t\t\t\tthis.store_itemPriceApi()\r\n\t\t\t},\r\n\t\t\t// 价格计算\r\n\t\t\tasync store_itemPriceApi() {\r\n\t\t\t\tlet str = ''\r\n\t\t\t\tthis.style.forEach(res => {\r\n\t\t\t\t\tstr += res.id + '_'\r\n\t\t\t\t})\r\n\t\t\t\tstr = str.substring(0, str.length - 1)\r\n\t\t\t\tlet data = await store_itemPrice({\r\n\t\t\t\t\tgoods_id: this.order_id,\r\n\t\t\t\t\tspu_id: str\r\n\t\t\t\t})\r\n\t\t\t\tthis.orderprive = Location.BumberPrecision(data.data.price)\r\n\t\t\t},\r\n\t\t\t// 添加购物车\r\n\t\t\tasync add_joinCar() {\r\n\t\t\t\tif (!uni.getStorageSync('userinfo')) {\r\n\t\t\t\t\tthis.enter = true\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tlet str = ''\r\n\t\t\t\tthis.style.forEach(res => {\r\n\t\t\t\t\tstr += res.id + '_'\r\n\t\t\t\t})\r\n\t\t\t\tstr = str.substring(0, str.length - 1)\r\n\t\t\t\tlet data = await order_joinCar({\r\n\t\t\t\t\tstore_id: this.store_default_data.id,\r\n\t\t\t\t\tgoods_id: this.order_id,\r\n\t\t\t\t\tspu_id: str,\r\n\t\t\t\t\tcount: 1,\r\n\t\t\t\t\torder_type: this.activate_data\r\n\r\n\t\t\t\t})\r\n\t\t\t\tif (data.code == 1) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: data.msg,\r\n\t\t\t\t\t\ticon: \"none\"\r\n\t\t\t\t\t})\r\n\t\t\t\t\tthis.order_carListApi()\r\n\t\t\t\t\tthis.show_specification = false\r\n\t\t\t\t\tthis.style = []\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: data.msg,\r\n\t\t\t\t\t\ticon: \"none\"\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 购物车列表\r\n\t\t\tasync order_carListApi() {\r\n\t\t\t\tlet token = uni.getStorageSync('userinfo').token\r\n\t\t\t\tif (!token) {\r\n\t\t\t\t\tthis.shopping_trolley_list = []\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tlet data = await order_carList()\r\n\t\t\t\tif (data.code == 1) {\r\n\t\t\t\t\tthis.shopping_trolley_list = data.data\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.shopping_trolley_list = []\r\n\t\t\t\t}\r\n\r\n\t\t\t},\r\n\t\t\t//结算前判断是否登录\r\n\t\t\tregister() {\r\n\t\t\t\tthis.enter = true\r\n\t\t\t},\r\n\t\t\trouter_close(url) {\r\n\t\t\t\tuni.reLaunch({\r\n\t\t\t\t\turl: url\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\troutergo(url) {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: url\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\troutergos(url) {\r\n\t\t\t\tlet token = uni.getStorageSync('TOKEN')\r\n\t\t\t\tlet user = uni.getStorageSync('user')\r\n\t\t\t\tif (token && user) {\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: url\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.enter = true\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t//未登录关闭弹出层需要关掉组件\r\n\t\t\tclosepage() {\r\n\t\t\t\tthis.enter = false\r\n\t\t\t},\r\n\t\t\trefresherpulling() {\r\n\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\tpage {\r\n\t\tbackground: #FFFFFF;\r\n\t}\r\n</style>\r\n<style lang=\"scss\" scoped>\r\n\t/* 定位失败悬浮提示样式 */\r\n\t.location-error-toast {\r\n\t\tposition: fixed;\r\n\t\ttop: 200rpx;\r\n\t\twidth: 690rpx;\r\n\t\tleft: 30rpx;\r\n\t\tbackground-color: rgba(0, 0, 0, 0.6);\r\n\t\tcolor: white;\r\n\t\tpadding: 15rpx 20rpx;\r\n\t\tbox-sizing: border-box;\r\n\t\tborder-radius: 12rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tz-index: 9999;\r\n\t\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);\r\n\t\tanimation: fadeIn 0.3s ease-in-out;\r\n\t}\r\n\r\n\t@keyframes fadeIn {\r\n\t\tfrom {\r\n\t\t\topacity: 0;\r\n\t\t\ttop: 0;\r\n\t\t}\r\n\r\n\t\tto {\r\n\t\t\topacity: 1;\r\n\t\t\ttop: 20rpx;\r\n\t\t}\r\n\t}\r\n\r\n\t.error-text {\r\n\t\tfont-size: 28rpx;\r\n\t\tmargin-right: 15rpx;\r\n\t}\r\n\r\n\t.retry-btn {\r\n\t\tbackground-color: #00C2A0;\r\n\t\tcolor: white;\r\n\t\tfont-size: 12px;\r\n\t\tpadding: 4px 12px;\r\n\t\tborder-radius: 4px;\r\n\t\tmargin: 0;\r\n\t}\r\n\r\n\t.movableArea {\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tpointer-events: none; //设置area元素不可点击，则事件便会下移至页面下层元素\r\n\t}\r\n\r\n\t.movableView {\r\n\t\tpointer-events: auto; //可以点击\r\n\t}\r\n\r\n\t.scrollBox {\r\n\t\tpadding: 0rpx 30rpx;\r\n\t\tbox-sizing: border-box;\r\n\t\theight: 80rpx;\r\n\t\tborder-bottom: 1rpx solid #F4F4F4;\r\n\r\n\t\t.menu {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\t// white-space: nowrap;\r\n\t\t\tscrollbar-width: none;\r\n\t\t\t-ms-overflow-style: none;\r\n\r\n\t\t\t&::-webkit-scrollbar {\r\n\t\t\t\tdisplay: none;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.menu-item {\r\n\t\t\tdisplay: inline-block;\r\n\t\t\t// 适当减小外边距\r\n\t\t\t// width: 195rpx;\r\n\t\t\theight: 80rpx;\r\n\t\t\t// line-height: 46rpx;\r\n\t\t\ttext-align: center;\r\n\t\t\t// font-weight: bold;\r\n\t\t\t// padding-bottom: 5rpx;\r\n\t\t\tfont-size: 30rpx;\r\n\t\t\tcolor: #414141;\r\n\t\t\tmargin-right: 30rpx;\r\n\t\t\ttransition: all 0.3s ease;\r\n\r\n\t\t\t&:last-child {\r\n\t\t\t\tmargin-right: 0;\r\n\t\t\t}\r\n\r\n\r\n\t\t}\r\n\r\n\t\t.active {\r\n\t\t\tcolor: #00C2A0;\r\n\t\t\tfont-size: 30rpx;\r\n\t\t\tfont-weight: bold;\r\n\t\t\tposition: relative;\r\n\r\n\t\t\t&::after {\r\n\t\t\t\tcontent: '';\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tz-index: 99;\r\n\t\t\t\tbottom: 20rpx;\r\n\t\t\t\tleft: 0;\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\theight: 6rpx;\r\n\t\t\t\tborder-radius: 50rpx;\r\n\t\t\t\tbackground-color: #00C2A0;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.search {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\r\n\t\t.search_left {\r\n\t\t\tfont-size: 32rpx;\r\n\t\t\tfont-weight: 600;\r\n\t\t\tcolor: #05B6F6;\r\n\t\t\tpadding-left: 30rpx;\r\n\r\n\t\t\timage {\r\n\t\t\t\twidth: 206rpx;\r\n\t\t\t\theight: 58rpx;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.search_right {\r\n\t\t\twidth: 62rpx;\r\n\t\t\theight: 62rpx;\r\n\t\t\tpadding: 0 10rpx;\r\n\t\t}\r\n\t}\r\n\r\n\t.hade_location {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t\tpadding: 20rpx 0 30rpx;\r\n\r\n\t\t.tag {\r\n\t\t\theight: 39rpx;\r\n\t\t\tborder-radius: 4rpx;\r\n\t\t\tborder: 1rpx solid #05B6F6;\r\n\t\t\tfont-size: 24rpx;\r\n\t\t\tfont-weight: 400;\r\n\t\t\tcolor: #05B6F6;\r\n\t\t\ttext-align: center;\r\n\t\t\tbox-sizing: border-box;\r\n\t\t\tpadding: 0 10rpx;\r\n\t\t\tmargin-right: 10rpx;\r\n\t\t\tline-height: 37rpx;\r\n\t\t}\r\n\r\n\t\t.hade_location_left {\r\n\t\t\tmargin-left: 20rpx;\r\n\r\n\t\t\t.hade_location_left_top {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tfont-weight: 600;\r\n\t\t\t\tcolor: #313131;\r\n\r\n\t\t\t\ttext {\r\n\t\t\t\t\t-webkit-line-clamp: 1; //设置几行\r\n\t\t\t\t\tdisplay: -webkit-box; //设置为伸缩盒弹性盒子展示\r\n\t\t\t\t\toverflow: hidden; //超出隐藏\r\n\t\t\t\t\ttext-overflow: ellipsis; //设置超出部分以省略号展示\r\n\t\t\t\t\t-webkit-box-orient: vertical; //伸缩盒弹性盒子的排列方式\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.schoolName {\r\n\t\t\t\t\theight: 45rpx;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\t\tcolor: #BE5C2D;\r\n\t\t\t\t\tpadding: 5rpx 10rpx 5rpx 5rpx;\r\n\t\t\t\t\tbackground: #FFF9E2;\r\n\t\t\t\t\tborder-radius: 12rpx;\r\n\r\n\t\t\t\t\timage {\r\n\t\t\t\t\t\twidth: 36rpx;\r\n\t\t\t\t\t\theight: 30rpx;\r\n\t\t\t\t\t\tmargin-right: 5rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.hade_location_left_down {\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\tfont-weight: 400;\r\n\t\t\t\tcolor: #666A6B;\r\n\t\t\t\tmargin-top: 5rpx;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.hade_location_right {\r\n\t\t\t// width: 180rpx;\r\n\t\t\theight: 63rpx;\r\n\t\t\tbackground: #F4F4F4;\r\n\t\t\tborder-radius: 32rpx;\r\n\t\t\tmargin-right: 20rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: space-between;\r\n\r\n\t\t\t.hade_location_right_content {\r\n\t\t\t\twidth: 90rpx;\r\n\t\t\t\theight: 63rpx;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tline-height: 63rpx;\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\tfont-weight: 400;\r\n\t\t\t\tcolor: #949494;\r\n\t\t\t}\r\n\r\n\t\t\t.hade_location_right_content_activate {\r\n\t\t\t\tbackground-color: #05B6F6;\r\n\t\t\t\tcolor: #FDFEFF;\r\n\t\t\t\tborder-radius: 32rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.felx {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\r\n\t\t.felx_right_box {\r\n\t\t\twidth: 571rpx;\r\n\t\t\theight: 67vh;\r\n\r\n\t\t\t.right_title {\r\n\t\t\t\tpadding: 20rpx 20rpx 0 20rpx;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tflex-wrap: wrap;\r\n\r\n\t\t\t\t.title {\r\n\t\t\t\t\twidth: 162rpx;\r\n\t\t\t\t\theight: 62rpx;\r\n\t\t\t\t\tline-height: 62rpx;\r\n\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\tcolor: #414141;\r\n\t\t\t\t\tbackground: #F6F7FB;\r\n\t\t\t\t\tborder-radius: 8rpx;\r\n\t\t\t\t\tmargin-right: 20rpx;\r\n\t\t\t\t\tmargin-bottom: 20rpx;\r\n\r\n\t\t\t\t\t&:nth-child(3n+3) {\r\n\t\t\t\t\t\tmargin-right: 0;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.title_active {\r\n\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\tcolor: #00C2A0;\r\n\t\t\t\t\tbackground: #DCF6ED;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.felx_right_box_conten {\r\n\t\t\t\twidth: 522rpx;\r\n\t\t\t\tmargin: 0 auto;\r\n\r\n\t\t\t\t.felx_right_box_conten_img {\r\n\t\t\t\t\twidth: 525rpx;\r\n\t\t\t\t\tborder-radius: 16rpx;\r\n\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.felx_right_box_conten_title {\r\n\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\tfont-weight: 600;\r\n\t\t\t\t\tcolor: #353535;\r\n\t\t\t\t\tpadding: 30rpx 0 30rpx 10rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.specification {\r\n\t\twidth: 688rpx;\r\n\t\tbackground: #FFFFFF;\r\n\t\tborder-radius: 12rpx;\r\n\t\tposition: relative;\r\n\t\tz-index: 10074;\r\n\r\n\t\t.specification_title {\r\n\t\t\ttext-align: center;\r\n\t\t\tfont-size: 32rpx;\r\n\t\t\tfont-weight: 600;\r\n\t\t\tcolor: #353535;\r\n\t\t\tmargin: 0 auto;\r\n\t\t}\r\n\r\n\t\t.specification_title_1 {\r\n\t\t\twidth: 95%;\r\n\t\t\theight: 504rpx;\r\n\t\t\tmargin: 0 auto;\r\n\r\n\t\t\t.specification_title_1_title {\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\tfont-family: PingFangSC-Regular, PingFang SC;\r\n\t\t\t\tfont-weight: 400;\r\n\t\t\t\tcolor: #676767;\r\n\t\t\t\tdisplay: inline-block;\r\n\t\t\t\tpadding: 20rpx 0;\r\n\r\n\t\t\t}\r\n\r\n\t\t\t.specification_title_1_content {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\toverflow-x: auto;\r\n\t\t\t\tdisplay: -webkit-box;\r\n\t\t\t\t-webkit-overflow-scrolling: touch;\r\n\r\n\t\t\t\t.specification_title_1_content_flex_activate {\r\n\t\t\t\t\tbackground: #F4FCFF !important;\r\n\t\t\t\t\tborder: 1rpx solid #00B8FB !important;\r\n\t\t\t\t\tcolor: #00B8FB !important;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.font_sizi_1 {\r\n\t\t\t\t\tcolor: #00B8FB;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.font_sizi_2 {\r\n\t\t\t\t\tborder-left: 1rpx solid #00B8FB;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.specification_title_1_content_flex {\r\n\t\t\t\t\theight: 63rpx;\r\n\t\t\t\t\tbackground: #FFFFFF;\r\n\t\t\t\t\tborder-radius: 14rpx;\r\n\t\t\t\t\tborder: 1rpx solid #F1F1F1;\r\n\t\t\t\t\tmargin-right: 20rpx;\r\n\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\tline-height: 63rpx;\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\tcolor: #343434;\r\n\t\t\t\t\tpadding: 0 40rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.close {\r\n\t\t\tposition: absolute;\r\n\t\t\tbottom: -150rpx;\r\n\t\t\tleft: 50%;\r\n\t\t\ttransform: translateX(-50%);\r\n\t\t}\r\n\t}\r\n\r\n\t.selected {\r\n\t\twidth: 688rpx;\r\n\t\tpadding: 20rpx 0;\r\n\t\tbackground: #F5F5F5;\r\n\t\tmargin-top: 60rpx;\r\n\r\n\t\ttext {\r\n\t\t\tfont-size: 24rpx;\r\n\t\t\tfont-weight: 400;\r\n\t\t\tcolor: #363636;\r\n\t\t}\r\n\r\n\t\ttext:nth-child(1) {\r\n\t\t\tfont-size: 24rpx;\r\n\t\t\tfont-weight: 400;\r\n\t\t\tcolor: #676767;\r\n\t\t\tpadding: 0 20rpx;\r\n\t\t\tmargin-left: 10rpx;\r\n\t\t}\r\n\t}\r\n\r\n\t.sublist {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t\tpadding: 30rpx 40rpx;\r\n\r\n\t\t.sublist_left {\r\n\t\t\ttext:nth-child(1) {\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tfont-weight: 600;\r\n\t\t\t\tcolor: #363636;\r\n\t\t\t}\r\n\r\n\t\t\ttext:nth-child(2) {\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tfont-weight: 600;\r\n\t\t\t\tcolor: #FF4000;\r\n\r\n\t\t\t\ttext:nth-child(1) {\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\tfont-weight: 600;\r\n\t\t\t\t\tcolor: #FF4000;\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.sublist_right {\r\n\t\t\twidth: 234rpx;\r\n\t\t\theight: 62rpx;\r\n\t\t\tbackground: #02B6FD;\r\n\t\t\tborder-radius: 12rpx;\r\n\t\t\tfont-size: 32rpx;\r\n\t\t\tfont-weight: 500;\r\n\t\t\tcolor: #F3FCFF;\r\n\t\t\tline-height: 62rpx;\r\n\t\t\ttext-align: center;\r\n\t\t}\r\n\t}\r\n\r\n\t.coupn {\r\n\t\t.coupn_icon {\r\n\t\t\twidth: 24rpx;\r\n\t\t\theight: 24rpx;\r\n\t\t\tmargin-left: 100rpx;\r\n\t\t\tmargin-bottom: 10rpx;\r\n\t\t\topacity: 0.5;\r\n\t\t}\r\n\r\n\t\t.coupn_img {\r\n\t\t\twidth: 130rpx;\r\n\t\t\theight: 121rpx;\r\n\t\t}\r\n\r\n\t\t.coupn_title {\r\n\t\t\twidth: 120rpx;\r\n\t\t\tpadding: 5rpx 10rpx;\r\n\t\t\tbackground: #FEDD5B;\r\n\t\t\tborder-radius: 18rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tposition: relative;\r\n\t\t\tbottom: 30rpx;\r\n\r\n\t\t\t.coupn_title_text {\r\n\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\tfont-weight: 400;\r\n\t\t\t\tcolor: #631407;\r\n\t\t\t\tmargin-left: 10rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./order.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./order.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754557237070\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./order.vue?vue&type=style&index=1&id=127632e4&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./order.vue?vue&type=style&index=1&id=127632e4&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754557252963\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}