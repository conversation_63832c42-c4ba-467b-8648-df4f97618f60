{"version": 3, "sources": ["uni-app:///main.js", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/pages/order_all/selectstore/selectstore.vue?f467", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/pages/order_all/selectstore/selectstore.vue?6371", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/pages/order_all/selectstore/selectstore.vue?5051", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/pages/order_all/selectstore/selectstore.vue?7191", "uni-app:///pages/order_all/selectstore/selectstore.vue", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/pages/order_all/selectstore/selectstore.vue?f679", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/pages/order_all/selectstore/selectstore.vue?8875"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "selectCity", "data", "index_select", "mapshow", "title", "latitude", "longitude", "covers", "shoplist", "location", "locationname", "page", "pcaCode", "onLoad", "onShow", "methods", "getopenLocation", "console", "uni", "name", "address", "success", "fail", "icon", "handleConfirm", "getList", "limit", "coordinate", "errCode", "msg", "store_listApi", "getlocation", "type", "callCustomerService", "content", "confirmText", "cancelText", "phoneNumber", "init", "id", "imgUrl", "lat", "lng", "that", "MapData", "arrayData", "iconPath", "width", "height", "callout", "color", "fontSize", "borderRadius", "bgColor", "padding", "display", "textAlign", "label", "x", "y", "borderWidth", "borderColor", "anchor", "markertap", "tap", "updated", "mapdisplay", "goplace", "url", "onReachBottom"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,oBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwH;AACxH;AAC+D;AACL;AACc;;;AAGxE;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,sFAAM;AACR,EAAE,+FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,0SAEN;AACP,KAAK;AACL;AACA,aAAa,qRAEN;AACP,KAAK;AACL;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACzDA;AAAA;AAAA;AAAA;AAAqmB,CAAgB,+nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;AC2GznB;AACA;AAOA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAGA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;EACAC;IACA;IACA;EAEA;EACAC,2BAEA;EACAC;IACAC;MACAC;MACAC;QACAb;QAAA;QACAC;QAAA;QACAa;QAAA;QACAC;QAAA;QACAC;UACAJ;QACA;QACAK;UACAL;UACAC;YACAd;YACAmB;UACA;QACA;MACA;IACA;IACAC;MACA;MACA;MAEA;MACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAOA;kBACAd;kBACAe;kBACAC;kBACA;kBACAf;gBAEA;cAAA;gBAAA;gBAVAX;gBACA2B;gBACAC;gBASA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;IACAC;MACA;IACA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACAb;kBACAc;kBACAX;oBACAJ;oBACA;oBACAC;sBACAd;sBACAmB;oBACA;oBACA;kBACA;gBAEA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;IACAU;MACA;MACAf;QACAd;QACA8B;QACAC;QACAC;QACAf;UACA;UACA;YACAH;cACAmB;cACAhB;gBACAJ;cACA;cACAK;gBACAL;gBACAC;kBACAd;kBACAmB;gBACA;cACA;YACA;UACA;QACA;MACA;IACA;IACAe;MACA;;MAEA;MACA;MACA;MACA;QACAC;QACApB;QACAqB;QACAC;QACAC;MACA;QACAH;QACApB;QACAqB;QACAC;QACAC;MACA;QACAH;QACApB;QACAqB;QACAC;QACAC;MACA;MACAC;IACA;IACA;IACAC;MACA3B;MACAA;MACA;MACA;QACA4B;UACAN;UAAA;UACAlC;UAAA;UACAC;UAAA;UACAF;UAAA;UACA0C;UAAA;UACAC;UACAC;UACAC;YACA;YACAf;YACAgB;YAAA;YACAC;YAAA;YACAC;YAAA;YACAC;YAAA;YACAC;YAAA;YACAC;YAAA;YACAC;UACA;;UACAC;YACA;YACAvB;YAAA;YACAgB;YAAA;YACAC;YAAA;YACAO;YAAA;YACAC;YAAA;YACAC;YAAA;YACAC;YAAA;YACAT;YAAA;YACAC;YACAC;YAAA;YACAE;UACA;;UACAM;YACA;YACAJ;YACAC;UACA;QACA;MACA;MACA1C;MACAA;MACA;MACA0B;IACA;IAEA;IACAoB;MACA9C;MACAA;MACA;IACA;IACA;IACA+C;MACA9C;QACAd;QACAmB;MACA;MACAN;MACAA;MACAC;MACA;IACA;IACA;IACA+C;MACAhD;MACAA;IACA;IACAiD;MACA;QACA;MACA;QACA;MACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAC;MACA;MACAlD;MACA;MACAC;QACAkD;MACA;IAGA,EACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACA;EACAC;IACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACpbA;AAAA;AAAA;AAAA;AAAgpC,CAAgB,snCAAG,EAAC,C;;;;;;;;;;;ACApqC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/order_all/selectstore/selectstore.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/order_all/selectstore/selectstore.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./selectstore.vue?vue&type=template&id=4b96ecb3&\"\nvar renderjs\nimport script from \"./selectstore.vue?vue&type=script&lang=js&\"\nexport * from \"./selectstore.vue?vue&type=script&lang=js&\"\nimport style0 from \"./selectstore.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/order_all/selectstore/selectstore.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./selectstore.vue?vue&type=template&id=4b96ecb3&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/@dcloudio/uni-ui/lib/uni-icons/uni-icons\" */ \"@dcloudio/uni-ui/lib/uni-icons/uni-icons.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uEmpty: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-empty/u-empty\" */ \"uview-ui/components/u-empty/u-empty.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.shoplist.length\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      ;(_vm.index_select = 0), _vm.store_listApi()\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./selectstore.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./selectstore.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<view class=\"hade\">\r\n\t\t\t<view class=\"hade_left\">\r\n\t\t\t\t<view @tap=\"index_select=0,store_listApi()\" :style=\"{color:index_select==0?'#05B6F6':'' }\">\r\n\t\t\t\t\t附近校区\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- <view @tap=\"index_select=1,store_listApi()\" :style=\"{color:index_select==1?'#05B6F6':'' }\">\r\n\t\t\t\t\t常去/收藏门店\r\n\t\t\t\t</view> -->\r\n\t\t\t\t<view class=\"right selectCity\">\r\n\t\t\t\t\t<select-city :type=\"type\" :districtCode=\"stuInfo.pcaCode\" @confirm=\"handleConfirm\" />\r\n\t\t\t\t\t<uni-icons class=\"icon-right\" type=\"right\" size=\"16\" color=\"#C3C3C3\"></uni-icons>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<!-- <view class=\"hade_right\" @tap=\"position_selection\">\r\n\t\t\t\t<view>\r\n\t\t\t\t\t<u-icon name=\"search\" color=\"#B6B6B6\" size=\"40\"></u-icon>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view>\r\n\t\t\t\t\t搜索\r\n\t\t\t\t</view>\r\n\t\t\t</view> -->\r\n\t\t</view>\r\n\t\t<view class=\"content\">\r\n\t\t\t<view class=\"content_hade\" @tap=\"position_selection\">\r\n\t\t\t\t<!-- <view class=\"content_hade_1\">\r\n\t\t\t\t\t<u-icon name=\"map\" color=\"#5A5A5A \" size=\"30\"></u-icon>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"content_hade_2\">\r\n\t\t\t\t\t{{locationname}}\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"content_hade_3\">\r\n\t\t\t\t\t<u-icon name=\"arrow-right\" color=\"#5A5A5A \" size=\"30\"></u-icon>\r\n\t\t\t\t</view> -->\r\n\t\t\t</view>\r\n\t\t\t<view class=\"box\">\r\n\t\t\t\t<map class=\"map\" :style=\"{height: mapshow==0?'399rpx':'100rpx'}\" :latitude=\"location.latitude\"\r\n\t\t\t\t\t:longitude=\"location.longitude\" :markers=\"covers\" :show-location='true' @markertap=\"markertap\"\r\n\t\t\t\t\t@tap=\"tap\" @updated=\"updated\"></map>\r\n\t\t\t\t<view class=\"display\" @tap=\"mapdisplay\">\r\n\t\t\t\t\t<view class=\"display_text\">\r\n\t\t\t\t\t\t<view class=\"display_text_content\" v-if=\"mapshow==0\">\r\n\t\t\t\t\t\t\t<text>收起地图 </text>\r\n\t\t\t\t\t\t\t<u-icon name=\"arrow-up\" color=\"#252525\" size=\"28\"></u-icon>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"display_text_content\" v-if=\"mapshow==1\">\r\n\t\t\t\t\t\t\t<text>展开地图 </text>\r\n\t\t\t\t\t\t\t<u-icon name=\"arrow-down\" color=\"#252525\" size=\"28\"></u-icon>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view style=\"margin-top: 150rpx;\" v-if=\"shoplist.length<1\">\r\n\t\t\t\t<u-empty mode=\"data\" :iconSize='150' :textSize='24' text='暂无内容' icon=\"\"></u-empty>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"position_box\" v-for=\"(item,index) in shoplist\" :key=\"item\">\r\n\t\t\t\t<view class=\"chosen_position\">\r\n\t\t\t\t\t<view class=\"chosen_position_left\">\r\n\t\t\t\t\t\t<view class=\"chosen_position_left_1\">\r\n\t\t\t\t\t\t\t{{item.name}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"chosen_position_left_2\">\r\n\t\t\t\t\t\t\t<u-icon name=\"map\" color=\"#5A5A5A \" size=\"28\"></u-icon>\r\n\t\t\t\t\t\t\t<text style=\"margin-left: 5rpx;\"> {{item.addr||item.selectedPlace}}</text>\r\n\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<!-- <view class=\"chosen_position_left_3\">\r\n\t\t\t\t\t\t\t<u-icon name=\"clock\" color=\"#5A5A5A \" size=\"28\"></u-icon>\r\n\t\t\t\t\t\t\t<text style=\"margin-left: 5rpx;\"> {{item.start_times}}-{{item.end_times}}</text>\r\n\t\t\t\t\t\t</view> -->\r\n\t\t\t\t\t\t<view class=\"chosen_position_left_4\">\r\n\t\t\t\t\t\t\t<view v-if=\"item.isOpen==1\">营业中</view>\r\n\t\t\t\t\t\t\t<view v-else>已歇业</view>\r\n\t\t\t\t\t\t\t<!-- <view v-if=\"item.subscribe_switch==1&&item.is_business==1\">接受预定</view>\r\n\t\t\t\t\t\t\t<view v-if=\"item.takeaway_switch==1&&item.is_business==1\">可外卖</view>\r\n\t\t\t\t\t\t\t<view v-if=\"item.delivery_switch==1&&item.is_business==1\">可自提</view> -->\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"chosen_position_right\">\r\n\t\t\t\t\t\t<view class=\"chosen_position_right_1\">\r\n\t\t\t\t\t\t\t<span @tap=\"goplace(item,1)\">去下单</span>\r\n\t\t\t\t\t\t\t<!-- <span v-if=\"item.subscribe_switch==1&&item.is_business==1\">/</span> -->\r\n\t\t\t\t\t\t\t<!-- <span @tap=\"goplace(item.id,2)\"\r\n\t\t\t\t\t\t\t\tv-if=\"item.subscribe_switch==1&&item.is_business==1\">去预约</span> -->\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"chosen_position_right_2\" v-if=\"item.distanceText\">\r\n\t\t\t\t\t\t\t距离{{ item.distanceText}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"chosen_position_right_3\">\r\n\t\t\t\t\t\t\t<view class=\"chosen_position_right_3_1\" @tap=\"openTel(item.tel)\">\r\n\t\t\t\t\t\t\t\t<image src=\"@/static/Project_drawing 15.png\" mode=\"\"></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"chosen_position_right_3_2\" @tap=\"getopenLocation(item)\">\r\n\t\t\t\t\t\t\t\t<image src=\"@/static/Project_drawing 13.png\" mode=\"\"></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport img from \"@/static/Project_drawing 10.png\"\r\n\timport {\r\n\t\t// store_list,\r\n\t\t// store_collectionList,\r\n\t\t// order_empty,\r\n\t\twxCampusList\r\n\t} from \"@/api/comm.js\"\r\n\timport selectCity from '@/components/select_city/select_city.vue';\r\n\timport wApi from \"@/utils/wxApi.js\"\r\n\timport {\r\n\t\tforEach\r\n\t} from \"lodash\";\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\tselectCity\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tindex_select: 0,\r\n\t\t\t\tmapshow: 0,\r\n\t\t\t\ttitle: '百度地图',\r\n\t\t\t\tlatitude: 34.7586,\r\n\t\t\t\tlongitude: 113.672307,\r\n\t\t\t\tcovers: [], //标记点地图数据\r\n\t\t\t\tshoplist: [],\r\n\t\t\t\tlocation: {}, //当前页面展示的小地图经纬度\r\n\t\t\t\tlocationname: '', //当前位置名字\r\n\t\t\t\tpage: 1, //下拉加载,\r\n\t\t\t\tpcaCode: '',\r\n\t\t\t};\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\tthis.$forceUpdate();\r\n\t\t\tthis.getlocation()\r\n\r\n\t\t},\r\n\t\tonShow() {\r\n\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tgetopenLocation(item) {\r\n\t\t\t\tconsole.log(item)\r\n\t\t\t\tuni.openLocation({\r\n\t\t\t\t\tlatitude: item.latitude * 1, // 纬度\r\n\t\t\t\t\tlongitude: item.longitude * 1, // 经度\r\n\t\t\t\t\tname: item.name, // 可选：地点名称\r\n\t\t\t\t\taddress: item.selectedPlace, // 可选：详细地址\r\n\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\tconsole.log('地图打开成功');\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\tconsole.log(err)\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '打开地图失败',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\thandleConfirm(result) {\r\n\t\t\t\tthis.index_select = 1\r\n\t\t\t\tthis.pcaCode = result\r\n\r\n\t\t\t\tthis.location = {}\r\n\t\t\t\tthis.getList()\r\n\t\t\t},\r\n\t\t\tasync getList() {\r\n\t\t\t\t// console.log(this.location)\r\n\t\t\t\t// console.log([this.location.longitude, this.location.latitude].join(','))\r\n\t\t\t\tconst {\r\n\t\t\t\t\tdata,\r\n\t\t\t\t\terrCode,\r\n\t\t\t\t\tmsg\r\n\t\t\t\t} = await wxCampusList({\r\n\t\t\t\t\tpage: 1,\r\n\t\t\t\t\tlimit: 20,\r\n\t\t\t\t\tcoordinate: [this.location.longitude, this.location.latitude].join(','),\r\n\t\t\t\t\t// coordinate: '',\r\n\t\t\t\t\tpcaCode: this.pcaCode || '',\r\n\r\n\t\t\t\t})\r\n\t\t\t\tif (errCode == 0) {\r\n\t\t\t\t\tthis.shoplist = data.data\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 点击附近位置\r\n\t\t\tstore_listApi() {\r\n\t\t\t\tthis.getlocation()\r\n\t\t\t},\r\n\t\t\tasync getlocation() {\r\n\t\t\t\tuni.getLocation({\r\n\t\t\t\t\ttype: 'gcj02',\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tconsole.log('res', res)\r\n\t\t\t\t\t\tthis.location = res;\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: `纬度:${res.latitude}, 经度:${res.longitude}`,\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tthis.getList()\r\n\t\t\t\t\t},\r\n\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 点击拨打电话\r\n\t\t\tcallCustomerService(phone) {\r\n\t\t\t\t// 1. 先弹窗确认\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: '提示',\r\n\t\t\t\t\tcontent: `确定拨打电话：${phone}吗？`,\r\n\t\t\t\t\tconfirmText: '确定',\r\n\t\t\t\t\tcancelText: '取消',\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\t// 2. 用户点击“确定”后直接拨号\r\n\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\tuni.makePhoneCall({\r\n\t\t\t\t\t\t\t\tphoneNumber: phone,\r\n\t\t\t\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\t\t\t\tconsole.log('拨号成功');\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\t\t\t\tconsole.error('拨号失败:', err);\r\n\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\ttitle: '拨号失败，请稍后再试',\r\n\t\t\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tinit() {\r\n\t\t\t\tlet that = this;\r\n\r\n\t\t\t\t//发起网络请求获取数据\r\n\t\t\t\t//用uni.request(OBJECT)方法\r\n\t\t\t\t//我这里模拟下数据\r\n\t\t\t\tvar data = [{\r\n\t\t\t\t\tid: 1,\r\n\t\t\t\t\tname: '雷军',\r\n\t\t\t\t\timgUrl: '../../static/Project_drawing 1.png',\r\n\t\t\t\t\tlat: \"34.7586\",\r\n\t\t\t\t\tlng: \"113.672307\"\r\n\t\t\t\t}, {\r\n\t\t\t\t\tid: 2,\r\n\t\t\t\t\tname: '李彦宏',\r\n\t\t\t\t\timgUrl: '../../static/Project_drawing 2.png',\r\n\t\t\t\t\tlat: \"34.763466\",\r\n\t\t\t\t\tlng: \"113.686285\"\r\n\t\t\t\t}, {\r\n\t\t\t\t\tid: 3,\r\n\t\t\t\t\tname: '马化腾',\r\n\t\t\t\t\timgUrl: '../../static/Project_drawing 3.png',\r\n\t\t\t\t\tlat: \"34.763412\",\r\n\t\t\t\t\tlng: \"113.680185\"\r\n\t\t\t\t}, ];\r\n\t\t\t\tthat.MapData(that, data)\r\n\t\t\t},\r\n\t\t\t//地图数据初始化\r\n\t\t\tMapData(that, data) {\r\n\t\t\t\tconsole.log(data.length)\r\n\t\t\t\tconsole.log(data)\r\n\t\t\t\tlet arrayData = [];\r\n\t\t\t\tfor (var i = 0; i < data.length; i++) {\r\n\t\t\t\t\tarrayData.push({\r\n\t\t\t\t\t\tid: data[i].id, //marker点击事件回调会返回此id。建议为每个marker设置上Number类型id，保证更新marker时有更好的性能。\r\n\t\t\t\t\t\tlatitude: data[i].lat, //纬度\r\n\t\t\t\t\t\tlongitude: data[i].lng, //经度\r\n\t\t\t\t\t\ttitle: data[i].name, //点击时显示，callout存在时将被忽略\r\n\t\t\t\t\t\ticonPath: data[i].imgUrl, //项目目录下的图片路径，支持相对路径写法，以'/'开头则表示相对小程序根目录；也支持临时路径\r\n\t\t\t\t\t\twidth: 60,\r\n\t\t\t\t\t\theight: 60,\r\n\t\t\t\t\t\tcallout: {\r\n\t\t\t\t\t\t\t//自定义标记点上方的气泡窗口\r\n\t\t\t\t\t\t\tcontent: data[i].name,\r\n\t\t\t\t\t\t\tcolor: '', //文本颜色\r\n\t\t\t\t\t\t\tfontSize: 16, //文字大小\r\n\t\t\t\t\t\t\tborderRadius: 20, //callout边框圆角\r\n\t\t\t\t\t\t\tbgColor: '', //背景色\r\n\t\t\t\t\t\t\tpadding: 6, //文本边缘留白\r\n\t\t\t\t\t\t\tdisplay: 'BYCLICK', //'BYCLICK':点击显示; 'ALWAYS':常显\r\n\t\t\t\t\t\t\ttextAlign: 'left', //文本对齐方式。有效值: left, right, center\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tlabel: {\r\n\t\t\t\t\t\t\t//为标记点旁边增加标签\r\n\t\t\t\t\t\t\tcontent: '', //标记点旁边的文字\r\n\t\t\t\t\t\t\tcolor: '#ff6600', //文本颜色\r\n\t\t\t\t\t\t\tfontSize: 16, //文字大小\r\n\t\t\t\t\t\t\tx: 0, //label的坐标，原点是 marker 对应的经纬度\r\n\t\t\t\t\t\t\ty: 0, //label的坐标，原点是 marker 对应的经纬度\r\n\t\t\t\t\t\t\tborderWidth: 1, //边框宽度\r\n\t\t\t\t\t\t\tborderColor: '', //边框颜色\r\n\t\t\t\t\t\t\tborderRadius: 10, //边框圆角\r\n\t\t\t\t\t\t\tbgColor: 'red',\r\n\t\t\t\t\t\t\tpadding: 6, //\t文本边缘留白\r\n\t\t\t\t\t\t\ttextAlign: 'left', //文本对齐方式。有效值: left, right, center\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tanchor: {\r\n\t\t\t\t\t\t\t//经纬度在标注图标的锚点，默认底边中点      {x, y}，x表示横向(0-1)，y表示竖向(0-1)。{x: .5, y: 1} 表示底边中点\r\n\t\t\t\t\t\t\tx: .5,\r\n\t\t\t\t\t\t\ty: 1\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t\tconsole.log(arrayData.length)\r\n\t\t\t\tconsole.log(arrayData)\r\n\t\t\t\t//重新给地图数据赋值covers \r\n\t\t\t\tthat.covers = arrayData;\r\n\t\t\t},\r\n\r\n\t\t\t//地图点击事件\r\n\t\t\tmarkertap(e) {\r\n\t\t\t\tconsole.log(\"===你点击了标记点===\")\r\n\t\t\t\tconsole.log(\"你点击的标记点ID是:\" + e.detail.markerId)\r\n\t\t\t\t//console.log(e)\r\n\t\t\t},\r\n\t\t\t//点击地图时触发; App-nuve、微信小程序2.9支持返回经纬度\r\n\t\t\ttap(e) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '选中附近位置',\r\n\t\t\t\t\ticon: \"success\"\r\n\t\t\t\t})\r\n\t\t\t\tconsole.log(\"===你点击了地图点===\")\r\n\t\t\t\tconsole.log(e)\r\n\t\t\t\tuni.setStorageSync('location', e.detail)\r\n\t\t\t\tthis.store_listApi()\r\n\t\t\t},\r\n\t\t\t//在地图渲染更新完成时触发\r\n\t\t\tupdated(e) {\r\n\t\t\t\tconsole.log(\"===在地图渲染更新完成时触发===\")\r\n\t\t\t\tconsole.log(e)\r\n\t\t\t},\r\n\t\t\tmapdisplay() {\r\n\t\t\t\tif (this.mapshow) {\r\n\t\t\t\t\tthis.mapshow = 0\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.mapshow = 1\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// async store_listApi() {\r\n\t\t\t// \tif (this.index_select == 0) {\r\n\t\t\t// \t\tlet data = await store_list({\r\n\t\t\t// \t\t\tpage: 1,\r\n\t\t\t// \t\t\tlimit: 10,\r\n\t\t\t// \t\t\tsearch: ''\r\n\t\t\t// \t\t})\r\n\t\t\t// \t\tconsole.log(data, '门店列表');\r\n\t\t\t// \t\tthis.shoplist = []\r\n\t\t\t// \t\tthis.locationname = data.data.address\r\n\t\t\t// \t\tif (!data?.data?.rows?.length) return\r\n\t\t\t// \t\tdata?.data?.rows?.forEach(res => {\r\n\t\t\t// \t\t\tres.distance = wApi.mkm(res.distance)\r\n\t\t\t// \t\t})\r\n\t\t\t// \t\tthis.shoplist.push(...data.data.rows)\r\n\t\t\t// \t} else {\r\n\t\t\t// \t\tlet data = await store_collectionList({\r\n\t\t\t// \t\t\tpage: 1,\r\n\t\t\t// \t\t\tlimit: 10,\r\n\t\t\t// \t\t\tsearch: ''\r\n\t\t\t// \t\t})\r\n\t\t\t// \t\tconsole.log(data, '门店列表');\r\n\t\t\t// \t\tthis.shoplist = []\r\n\t\t\t// \t\tif (data.data) {\r\n\t\t\t// \t\t\tdata.data.rows.forEach(res => {\r\n\t\t\t// \t\t\t\tres.distance = wApi.mkm(res.distance)\r\n\t\t\t// \t\t\t})\r\n\t\t\t// \t\t\tthis.locationname = data.data.address\r\n\t\t\t// \t\t\tthis.shoplist.push(...data.data.rows)\r\n\t\t\t// \t\t}\r\n\t\t\t// \t}\r\n\t\t\t// },\r\n\t\t\t// async store_collectionListApi() {\r\n\t\t\t// \tlet data = await store_collectionList({\r\n\t\t\t// \t\tpage: '',\r\n\t\t\t// \t\tlimit: '',\r\n\t\t\t// \t})\r\n\t\t\t// },\r\n\t\t\t// async position_selection() {\r\n\t\t\t// \tlet data = await wApi.getchooseLocation()\r\n\t\t\t// \tconsole.log(data);\r\n\t\t\t// \tuni.setStorageSync('location', data)\r\n\t\t\t// \tthis.store_listApi()\r\n\t\t\t// },\r\n\t\t\tgoplace(item, e) {\r\n\t\t\t\t// uni.setStorageSync('shop', item)\r\n\t\t\t\tconsole.log(item)\r\n\t\t\t\t// order_empty()\r\n\t\t\t\tuni.reLaunch({\r\n\t\t\t\t\turl: `/pages/order/order?item=${JSON.stringify(item)}`\r\n\t\t\t\t})\r\n\r\n\r\n\t\t\t},\r\n\t\t\t// // 下拉加载\r\n\t\t\t// async store_listApi_down() {\r\n\t\t\t// \tif (this.index_select == 0) {\r\n\t\t\t// \t\tlet data = await store_list({\r\n\t\t\t// \t\t\tpage: this.page,\r\n\t\t\t// \t\t\tlimit: 10,\r\n\t\t\t// \t\t\tsearch: ''\r\n\t\t\t// \t\t})\r\n\t\t\t// \t\tconsole.log(data, '门店列表');\r\n\t\t\t// \t\tdata.data.rows.forEach(res => {\r\n\t\t\t// \t\t\tres.distance = wApi.mkm(res.distance)\r\n\t\t\t// \t\t})\r\n\t\t\t// \t\tthis.locationname = data.data.address\r\n\t\t\t// \t\tthis.shoplist.push(...data.data.rows)\r\n\t\t\t// \t} else {\r\n\t\t\t// \t\tlet data = await store_collectionList({\r\n\t\t\t// \t\t\tpage: this.page,\r\n\t\t\t// \t\t\tlimit: 10,\r\n\t\t\t// \t\t\tsearch: ''\r\n\t\t\t// \t\t})\r\n\t\t\t// \t\tconsole.log(data, '门店列表');\r\n\t\t\t// \t\tif (data.data) {\r\n\t\t\t// \t\t\tdata.data.rows.forEach(res => {\r\n\t\t\t// \t\t\t\tres.distance = wApi.mkm(res.distance)\r\n\t\t\t// \t\t\t})\r\n\t\t\t// \t\t\tthis.locationname = data.data.address\r\n\t\t\t// \t\t\tthis.shoplist.push(...data.data.rows)\r\n\t\t\t// \t\t}\r\n\t\t\t// \t}\r\n\t\t\t// },\r\n\t\t},\r\n\t\tonReachBottom() {\r\n\t\t\tthis.page++\r\n\t\t\t// this.store_listApi_down()\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.hade {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t\tbackground-color: #fff;\r\n\t\tpadding: 20rpx 0;\r\n\r\n\t\t.hade_left {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tfont-weight: 400;\r\n\t\t\tcolor: #323232;\r\n\r\n\t\t\tview:nth-child(1) {\r\n\t\t\t\tpadding: 0 30rpx;\r\n\t\t\t}\r\n\r\n\t\t\tview:nth-child(2) {\r\n\t\t\t\t// padding: 0 30rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.selectCity {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tpadding: 10rpx 15rpx;\r\n\t\t\t\tbox-sizing: border-box;\r\n\t\t\t\tborder-radius: 12rpx;\r\n\t\t\t\tborder: 1rpx solid #C3C3C3;\r\n\t\t\t}\r\n\r\n\t\t}\r\n\r\n\t\t.hade_right {\r\n\t\t\twidth: 141rpx;\r\n\t\t\theight: 66rpx;\r\n\t\t\tbackground: #F7F7F7;\r\n\t\t\tborder-radius: 33rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\ttext-align: center;\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tfont-weight: 400;\r\n\t\t\tcolor: #7A7A7A;\r\n\t\t\tmargin-right: 20rpx;\r\n\r\n\t\t\tview:nth-child(1) {\r\n\t\t\t\tmargin-left: 10rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.content {\r\n\t\twidth: 704rpx;\r\n\t\tmargin: 0 auto;\r\n\r\n\t\t.content_hade {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tpadding: 30rpx 0;\r\n\r\n\t\t\t.content_hade_2 {\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tfont-weight: 400;\r\n\t\t\t\tcolor: #373737;\r\n\t\t\t\tpadding: 0 10rpx;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.map {\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 399rpx;\r\n\t\t}\r\n\r\n\t\t.display {\r\n\t\t\theight: 67rpx;\r\n\t\t\tbackground: #FFFFFF;\r\n\t\t\tborder-radius: 0rpx 0rpx 6rpx 6rpx;\r\n\r\n\t\t\t.display_text {\r\n\t\t\t\twidth: 150rpx;\r\n\t\t\t\tmargin: 0 auto;\r\n\r\n\t\t\t\t.display_text_content {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tpadding: 15rpx 0;\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\tcolor: #373737;\r\n\r\n\t\t\t\t\ttext {\r\n\t\t\t\t\t\tmargin-right: 10rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.position_box {\r\n\t\t\t// height: 273rpx;\r\n\t\t\tbackground: #FFFFFF;\r\n\t\t\tborder-radius: 6rpx;\r\n\t\t\tmargin: 20rpx 0;\r\n\r\n\t\t\t.chosen_position {\r\n\t\t\t\tpadding: 30rpx;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: space-between;\r\n\r\n\t\t\t\t.chosen_position_left {\r\n\t\t\t\t\twidth: 70%;\r\n\r\n\t\t\t\t\t.chosen_position_left_1 {\r\n\t\t\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\t\t\tfont-weight: 600;\r\n\t\t\t\t\t\tcolor: #060606;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.chosen_position_left_2 {\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\t\tcolor: #676767;\r\n\t\t\t\t\t\tmargin-top: 15rpx;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.chosen_position_left_3 {\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\t\tcolor: #676767;\r\n\t\t\t\t\t\tmargin-top: 15rpx;\r\n\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.chosen_position_left_4 {\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\tmargin-top: 15rpx;\r\n\t\t\t\t\t\t// margin-left: 10rpx;\r\n\r\n\t\t\t\t\t\tview:nth-child(1) {\r\n\t\t\t\t\t\t\theight: 39rpx;\r\n\t\t\t\t\t\t\tborder-radius: 4rpx;\r\n\t\t\t\t\t\t\tborder: 1rpx solid #05B6F6;\r\n\t\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\t\t\tcolor: #05B6F6;\r\n\t\t\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\t\t\tbox-sizing: border-box;\r\n\t\t\t\t\t\t\tpadding: 0 10rpx;\r\n\t\t\t\t\t\t\t// margin-right: 10rpx;\r\n\t\t\t\t\t\t\tmargin: 0 10rpx;\r\n\t\t\t\t\t\t\tline-height: 37rpx;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\tview:nth-child(2) {\r\n\t\t\t\t\t\t\theight: 39rpx;\r\n\t\t\t\t\t\t\tbackground: #05B6F6;\r\n\t\t\t\t\t\t\tborder-radius: 4rpx;\r\n\t\t\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\t\t\tcolor: #F3F8FF;\r\n\t\t\t\t\t\t\tmargin-right: 10rpx;\r\n\t\t\t\t\t\t\tpadding: 0 10rpx;\r\n\t\t\t\t\t\t\tline-height: 39rpx;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\tview:nth-child(3) {\r\n\t\t\t\t\t\t\theight: 39rpx;\r\n\t\t\t\t\t\t\tbackground: #05B6F6;\r\n\t\t\t\t\t\t\tborder-radius: 4rpx;\r\n\t\t\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\t\t\tcolor: #F3F8FF;\r\n\t\t\t\t\t\t\tmargin-right: 10rpx;\r\n\t\t\t\t\t\t\tpadding: 0 10rpx;\r\n\t\t\t\t\t\t\tline-height: 39rpx;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\tview:nth-child(4) {\r\n\t\t\t\t\t\t\theight: 39rpx;\r\n\t\t\t\t\t\t\tbackground: #05B6F6;\r\n\t\t\t\t\t\t\tborder-radius: 4rpx;\r\n\t\t\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\t\t\tcolor: #F3F8FF;\r\n\t\t\t\t\t\t\tpadding: 0 10rpx;\r\n\t\t\t\t\t\t\tline-height: 39rpx;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.chosen_position_right {\r\n\t\t\t\t\twidth: 180rpx;\r\n\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\tborder-left: 1rpx solid #F0F0F0;\r\n\t\t\t\t\tpadding-left: 20rpx;\r\n\r\n\t\t\t\t\t.chosen_position_right_1 {\r\n\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\t\tcolor: #05B6F6;\r\n\t\t\t\t\t\tpadding: 20rpx 0;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.chosen_position_right_2 {\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\t\tcolor: #666666;\r\n\t\t\t\t\t\tmargin-bottom: 20rpx;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.chosen_position_right_3 {\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\tjustify-content: space-between;\r\n\r\n\t\t\t\t\t\t.chosen_position_right_3_1 {\r\n\t\t\t\t\t\t\twidth: 66rpx;\r\n\t\t\t\t\t\t\theight: 66rpx;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t.chosen_position_right_3_2 {\r\n\t\t\t\t\t\t\twidth: 66rpx;\r\n\t\t\t\t\t\t\theight: 66rpx;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./selectstore.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./selectstore.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754557257067\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}