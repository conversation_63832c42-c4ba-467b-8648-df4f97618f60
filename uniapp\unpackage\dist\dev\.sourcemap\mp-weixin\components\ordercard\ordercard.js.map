{"version": 3, "sources": ["webpack:///G:/code/uniapp/daxuetonguni/uniapp/components/ordercard/ordercard.vue?3f96", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/components/ordercard/ordercard.vue?9eb5", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/components/ordercard/ordercard.vue?7bbe", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/components/ordercard/ordercard.vue?9e86", "uni-app:///components/ordercard/ordercard.vue", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/components/ordercard/ordercard.vue?9042", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/components/ordercard/ordercard.vue?cd15"], "names": ["name", "props", "content", "type", "default", "required", "store_id", "activate_data", "data", "enter", "cartItems", "user", "onShow", "methods", "updateCartInfo", "add_joinCar", "uni", "existingItemIndex", "newItem", "val", "quantity", "title", "icon", "console", "routergo", "url", "stop", "e", "closepage"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;AACc;;;AAGtE;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,4NAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAmmB,CAAgB,6nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;AC4CvnB;AAEA;AAAA;AAAA,eACA;EACAA;EACAC;IACAC;MACAC;MACAC;MACAC;IACA;IACAC;MACAH;MACAC;MACAC;IACA;IACAE;MACAJ;MACAC;MACAC;IACA;EACA;EACAG;IACA;MACAC;MACAC;MAAA;MACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACAC;MACA;MACA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACAC;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAGA;gBACAC;kBAAA;gBAAA;gBAEA;kBACA;kBACA;gBACA;kBACA;kBACAC,0CACAC;oBACAC;kBAAA;kBAEA;kBACA;gBACA;;gBACAJ;kBACAK;kBACAC;gBACA;gBACA;gBACAN;gBACAO;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEAC;MACAR;QACAS;MACA;IACA;IACAC;MACA;QACAC;QACAxB;MACA;MACA;IACA;IACA;IACAyB;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACvIA;AAAA;AAAA;AAAA;AAA8oC,CAAgB,onCAAG,EAAC,C;;;;;;;;;;;ACAlqC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/ordercard/ordercard.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./ordercard.vue?vue&type=template&id=508e5c82&\"\nvar renderjs\nimport script from \"./ordercard.vue?vue&type=script&lang=js&\"\nexport * from \"./ordercard.vue?vue&type=script&lang=js&\"\nimport style0 from \"./ordercard.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/ordercard/ordercard.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./ordercard.vue?vue&type=template&id=508e5c82&\"", "var components\ntry {\n  components = {\n    login: function () {\n      return import(\n        /* webpackChunkName: \"components/login/login\" */ \"@/components/login/login.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./ordercard.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./ordercard.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<!-- 商品卡牌 -->\r\n\t<view>\r\n\t\t<block>\r\n\t\t\t<view class=\"box\" v-for=\"(item,index) in content\" :key=\"item\">\r\n\t\t\t\t<view class=\"box_left\">\r\n\t\t\t\t\t<view class=\"box_left_img\"\r\n\t\t\t\t\t\t@tap=\"routergo('/pages/order_all/goodsordersearch/goodsordersearch?id='+item.id)\">\r\n\t\t\t\t\t\t<image :src=\"item.course_cover\" mode=\"aspectFill\"></image>\r\n\t\t\t\t\t\t<!-- <view class=\"box_left_img_text\" v-if=\"item.tags\"> {{item.tags}}</view> -->\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"box_right\">\r\n\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t\t<text class=\"box_right_title\"\r\n\t\t\t\t\t\t\t@tap=\"routergo('/pages/order_all/goodsordersearch/goodsordersearch?id='+item.id)\">\r\n\t\t\t\t\t\t\t{{item.name}}</text>\r\n\t\t\t\t\t\t<!-- <view class=\"box_right_ranking\" v-if=\"item.xl_desc\">\r\n\t\t\t\t\t\t好老师 好资料 好服务\r\n\t\t\t\t\t</view> -->\r\n\t\t\t\t\t\t<view class=\"box_right_sell\">\r\n\t\t\t\t\t\t\t好老师 好资料 好服务\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"box_right_price\">\r\n\t\t\t\t\t\t<view class=\"box_right_price_left\">\r\n\t\t\t\t\t\t\t¥{{item.checkout_price}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<!-- <view v-if=\"item.sku_count>0\" class=\"box_right_price_right\" @tap=\"stop(item.id,item.type)\">\r\n\t\t\t\t\t\t\t<text> 选规格</text>\r\n\t\t\t\t\t\t</view> -->\r\n\t\t\t\t\t\t<view @click=\"add_joinCar(item)\" class=\"box_right_price_right_1\" v-if=\"!user.user\">\r\n\t\t\t\t\t\t\t<text>添加</text>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</block>\r\n\t\t<login v-if=\"enter\" @loadpage=\"unloadpage\" @closepage='closepage'></login>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\torder_joinCar,\r\n\t} from \"@/api/comm.js\"\r\n\texport default {\r\n\t\tname: \"ordercard\",\r\n\t\tprops: {\r\n\t\t\tcontent: {\r\n\t\t\t\ttype: Array,\r\n\t\t\t\tdefault: [],\r\n\t\t\t\trequired: true\r\n\t\t\t},\r\n\t\t\tstore_id: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ' ',\r\n\t\t\t\trequired: true\r\n\t\t\t},\r\n\t\t\tactivate_data: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: '',\r\n\t\t\t\trequired: true\r\n\t\t\t},\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tenter: '',\r\n\t\t\t\tcartItems: [], // 存储购物车商品信息\r\n\t\t\t\tuser: uni.getStorageSync('user') || {}\r\n\t\t\t};\r\n\t\t},\r\n\t\tonShow() {\r\n\t\t\t// 页面显示时更新购物车信息\r\n\t\t\tthis.updateCartInfo();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tupdateCartInfo() {\r\n\t\t\t\tconst cartData = uni.getStorageSync('cartItems');\r\n\t\t\t\tif (cartData) {\r\n\t\t\t\t\tthis.cartItems = cartData;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.cartItems = [];\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 添加购物车\r\n\t\t\tasync add_joinCar(val) {\r\n\t\t\t\tif (!uni.getStorageSync('TOKEN')) {\r\n\t\t\t\t\tthis.$emit('login')\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tthis.updateCartInfo()\r\n\t\t\t\tconst existingItemIndex = this.cartItems.findIndex(cartItem => cartItem.id === val.id);\r\n\r\n\t\t\t\tif (existingItemIndex !== -1) {\r\n\t\t\t\t\t// 商品已存在，增加数量\r\n\t\t\t\t\tthis.cartItems[existingItemIndex].quantity += this.cartItems[existingItemIndex].min_quantity\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 商品不存在，添加新商品\r\n\t\t\t\t\tconst newItem = {\r\n\t\t\t\t\t\t...val,\r\n\t\t\t\t\t\tquantity: val.min_quantity\r\n\t\t\t\t\t};\r\n\t\t\t\t\tthis.cartItems.push(newItem);\r\n\t\t\t\t\t// this.cartCount++;\r\n\t\t\t\t}\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '加入成功',\r\n\t\t\t\t\ticon: \"success\"\r\n\t\t\t\t})\r\n\t\t\t\t// 存储到本地\r\n\t\t\t\tuni.setStorageSync('cartItems', this.cartItems);\r\n\t\t\t\tconsole.log(this.cartItems)\r\n\t\t\t\tthis.$emit('addCar')\r\n\t\t\t},\r\n\r\n\t\t\troutergo(url) {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: url\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tstop(e, type) {\r\n\t\t\t\tlet obj = {\r\n\t\t\t\t\te,\r\n\t\t\t\t\ttype\r\n\t\t\t\t}\r\n\t\t\t\tthis.$emit('specification', obj)\r\n\t\t\t},\r\n\t\t\t//未登录关闭弹出层需要关掉组件\r\n\t\t\tclosepage() {\r\n\t\t\t\tthis.enter = false\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.box {\r\n\t\tpadding: 30rpx 0;\r\n\t\tdisplay: flex;\r\n\t\tborder-bottom: 1rpx solid #E6E6E6;\r\n\r\n\t\t.box_left {\r\n\t\t\t.box_left_img {\r\n\t\t\t\twidth: 220rpx;\r\n\t\t\t\theight: 164rpx;\r\n\t\t\t\tposition: relative;\r\n\r\n\t\t\t\timage {\r\n\t\t\t\t\tborder-radius: 11rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.box_left_img_text {\r\n\t\t\t\t\twidth: 63rpx;\r\n\t\t\t\t\theight: 36rpx;\r\n\t\t\t\t\tbackground: #05B6F6;\r\n\t\t\t\t\tborder-radius: 8rpx;\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\tcolor: #FDFEFF;\r\n\t\t\t\t\tline-height: 36rpx;\r\n\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\tpadding: 3rpx 5rpx;\r\n\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\ttop: -10rpx;\r\n\t\t\t\t\tright: -5rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.box_right {\r\n\t\t\twidth: 300rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-direction: column;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\tmargin-left: 15rpx;\r\n\r\n\t\t\t.box_right_title {\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tcolor: #414141;\r\n\t\t\t\tdisplay: inline-block;\r\n\t\t\t\tmargin-bottom: 15rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.box_right_ranking {\r\n\t\t\t\twidth: 182rpx;\r\n\t\t\t\theight: 34rpx;\r\n\t\t\t\tbackground: #ECF3FF;\r\n\t\t\t\tborder-radius: 6rpx;\r\n\t\t\t\tline-height: 34rpx;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\tfont-weight: 400;\r\n\t\t\t\tcolor: #05B6F6;\r\n\t\t\t\tmargin-bottom: 20rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.box_right_sell {\r\n\t\t\t\tfont-weight: 400;\r\n\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\tcolor: #777777;\r\n\t\t\t\tmargin-bottom: 20rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.box_right_price {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: space-between;\r\n\r\n\t\t\t\t.box_right_price_left {\r\n\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t\tcolor: #FB4E44;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.box_right_price_right_1 {\r\n\t\t\t\t\twidth: 92rpx;\r\n\t\t\t\t\theight: 45rpx;\r\n\t\t\t\t\tbackground: #00C2A0;\r\n\t\t\t\t\tborder-radius: 30rpx;\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\tcolor: #FFFFFF;\r\n\t\t\t\t\tline-height: 45rpx;\r\n\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\tposition: relative;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.box_right_price_right {\r\n\t\t\t\t\twidth: 101rpx;\r\n\t\t\t\t\theight: 47rpx;\r\n\t\t\t\t\tbackground: #05B6F6;\r\n\t\t\t\t\tborder-radius: 17rpx;\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\tcolor: #FDFEFF;\r\n\t\t\t\t\tline-height: 47rpx;\r\n\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\tposition: relative;\r\n\r\n\t\t\t\t\t.box_right_price_right_count {\r\n\t\t\t\t\t\twidth: 30rpx;\r\n\t\t\t\t\t\theight: 30rpx;\r\n\t\t\t\t\t\tbackground: #F65329;\r\n\t\t\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\t\tline-height: 30rpx;\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\tfont-weight: 600;\r\n\t\t\t\t\t\tcolor: #FFF8F6;\r\n\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\ttop: -10rpx;\r\n\t\t\t\t\t\tright: -10rpx;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./ordercard.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./ordercard.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754557258925\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}