{"version": 3, "sources": ["uni-app:///main.js", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/subpkg/report_all/report_view/report_view.vue?ca46", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/subpkg/report_all/report_view/report_view.vue?8392", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/subpkg/report_all/report_view/report_view.vue?8fc9", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/subpkg/report_all/report_view/report_view.vue?4810", "uni-app:///subpkg/report_all/report_view/report_view.vue", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/subpkg/report_all/report_view/report_view.vue?2925", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/subpkg/report_all/report_view/report_view.vue?7e66"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "zzsh_zonghe", "jyzb_zonghe", "schoolInfo", "type", "label", "content", "careerDirection", "educationPlanning", "academicAbilityEnhancement", "universityPlanning", "organizationalLife", "key", "adviceList", "reportData", "done", "planningList", "title", "zzsh", "list", "xygh", "sxbk", "jyzb", "from", "reportStatus", "computed", "components", "blueTitle", "reportContent", "reportContentTwo", "onLoad", "created", "methods", "getUserDetails", "res", "uni", "getReports", "prepareReport", "result", "url", "console", "getReportData", "userInfo", "name", "gender", "joinYear", "schoolName", "collegeName", "majorName", "personality", "postGraduationLabel", "sportsInterest", "artInterest", "academicInterest", "collegePlan", "totalScore", "rank", "position", "chineseScore", "mathScore", "foreignLangScore", "physicsScore", "chemistryScore", "biologyScore", "politicsScore", "historyScore", "geographyScore", "school", "logo", "tags", "item", "grade1", "Object", "icon", "getPostGraduationLabel", "back"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,oBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoI;AACpI;AAC+D;AACL;AACsC;;;AAGhG;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,kGAAM;AACR,EAAE,2GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,0SAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAqmB,CAAgB,+nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;AC6PznB;AAQA;AAGA;AAEA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,EACA;MAEA;MACAC;QACAH;QACAC;MACA,GACA;QACAD;QACAC;MACA,GACA;QACAD;QACAC;MACA,EACA;MACAG;QACAJ;QACAC;MACA,GACA;QACAD;QACAC;MACA,GACA;QACAD;QACAC;MACA,GACA;QACAD;QACAC;MACA,EACA;MACAI;QACAL;QACAC;MACA,GACA;QACAD;QACAC;MACA,GACA;QACAD;QACAC;MACA,GACA;QACAD;QACAC;MACA,GACA;QACAD;QACAC;MACA,GACA;QACAD;QACAC;MACA,EACA;MACAK,sBAEA;MACAC;QACAP;QACAC;QACAO;MACA,GACA;QACAR;QACAC;QACAO;MACA,GACA;QACAR;QACAC;QACAO;MACA,GACA;QACAR;QACAC;QACAO;MACA,EACA;MACAC;MACAC;QACAC;MACA;MACAC;QACAC;QACAX;UACAY;YACAD;YACAE;UACA;UACAC;YACAH;YACAE;UACA;UACAE;YACAJ;YACAE;UACA;UACAG;YACAL;YACAE;UACA;QACA;MACA;MACAnB;MACAuB;MAAA;MACAC;IACA;EACA;;EACAC,4BACA,8DACA;EACAC;IACAC;IACAC;IACAC;EACA;EACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBACA;kBACA;kBACA;kBACA;kBACA;oBACA;kBACA;oBACA;kBACA;gBACA;kBACAC;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAGA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAC;cAAA;gBAAAC;gBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACAH;oBACAI;kBACA;gBACA;kBACAJ;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAK;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OASA;cAAA;gBAAAP;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAGA;gBACAQ;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACA;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;gBACA,GAEA;gBACA/D;kBACAgE;oBACAC;oBACAzB;oBACA0B;kBACA;gBACA,GAEA;gBACA;kBAAA,uCACAC;oBACAhE;kBAAA;gBAAA,CACA;;gBAEA;gBACA;kBAAA,uCACAgE;oBACAhE;kBAAA;gBAAA,CACA;;gBAEA;gBACA;kBAAA,uCACAgE;oBACAhE;kBAAA;gBAAA,CACA;;gBAEA;gBACA;kBAAA,uCACAgE;oBACAhE;kBAAA;gBAAA,CACA;;gBAEA;gBACA;kBACA;oBAAA;sBACAF;sBACAC;sBACAC;oBACA;kBAAA;gBACA;;gBAEA;gBACA;kBACAiE;kBACAC;oBACA;sBACA;oBACA;kBACA;gBACA;kBACAA;oBACA;sBACA;oBACA;kBACA;gBACA;gBACA;kBACA;oBACA7B;oBACArC;kBACA;gBACA;gBACA;kBACA;oBACAqC;oBACArC;kBACA;gBACA;gBACA;gBACA;gBACA;gBAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAkC;gBACAL;kBACAlB;kBACAwD;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;QACAxC;UACAI;QACA;QACA;MACA;MAEA;MACA;MACAJ;QACAI;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACpnBA;AAAA;AAAA;AAAA;AAAwqC,CAAgB,8oCAAG,EAAC,C;;;;;;;;;;;ACA5rC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "subpkg/report_all/report_view/report_view.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './subpkg/report_all/report_view/report_view.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./report_view.vue?vue&type=template&id=475f855d&scoped=true&\"\nvar renderjs\nimport script from \"./report_view.vue?vue&type=script&lang=js&\"\nexport * from \"./report_view.vue?vue&type=script&lang=js&\"\nimport style0 from \"./report_view.vue?vue&type=style&index=0&id=475f855d&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"475f855d\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"subpkg/report_all/report_view/report_view.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./report_view.vue?vue&type=template&id=475f855d&scoped=true&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/@dcloudio/uni-ui/lib/uni-icons/uni-icons\" */ \"@dcloudio/uni-ui/lib/uni-icons/uni-icons.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./report_view.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./report_view.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"container\">\r\n\t\t<!-- 无报告信息时的展示 -->\r\n\t\t<view v-if=\"!reportData.done\" class=\"no-report\">\r\n\t\t\t<!-- 添加头部导航栏 -->\r\n\t\t\t<view class=\"header\">\r\n\t\t\t\t<view class=\"nav-bar\">\r\n\t\t\t\t\t<uni-icons @tap=\"back\" class=\"back-icon\" type=\"left\" size=\"20\" color=\"#fff\"></uni-icons>\r\n\t\t\t\t\t<text class=\"title\">报告详情</text>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t</view>\r\n\t\t\t<image class=\"no-data-img\"\r\n\t\t\t\tsrc=\"https://yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/h5/no_data.png\" mode=\"widthFix\">\r\n\t\t\t</image>\r\n\t\t\t<view class=\"no-data-tip\">\r\n\t\t\t\t<uni-icons type=\"trash\" size=\"44\" color=\"#999\"></uni-icons>\r\n\t\t\t\t<text class=\"no-data-text\">尚无报告信息</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 有报告信息时的展示 -->\r\n\t\t<template v-else>\r\n\t\t\t<view class=\"header\">\r\n\t\t\t\t<button class=\"transparent-button\" @click=\"back\">\r\n\t\t\t\t\t<uni-icons class=\"left-icon\" type=\"left\" size=\"20\"></uni-icons>\r\n\t\t\t\t</button>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"header\">\r\n\r\n\t\t\t\t<image class=\"header-img\"\r\n\t\t\t\t\tsrc=\"https://yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/h5/report_header_bg.png\"\r\n\t\t\t\t\tmode=\" widthFix\"></image>\r\n\t\t\t\t<text class=\"teacher-info\">学生：{{ reportUserInfo.name }}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"content\">\r\n\t\t\t\t<view class=\"base-info\">\r\n\t\t\t\t\t<blue-title :title=\"'第一部分：个人基础信息'\"></blue-title>\r\n\t\t\t\t\t<view class=\"base-info-item-container\">\r\n\t\t\t\t\t\t<view class=\"base-info-item width-25\">\r\n\t\t\t\t\t\t\t<text class=\"item-title\">学员姓名：</text>\r\n\t\t\t\t\t\t\t<text class=\"item\">{{ reportUserInfo.name }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"base-info-item\">\r\n\t\t\t\t\t\t\t<text class=\"item-title\">性别：</text>\r\n\t\t\t\t\t\t\t<view class=\"item\" v-if=\"reportUserInfo.gender ==0\">\r\n\t\t\t\t\t\t\t\t未知\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"item\" v-if=\"reportUserInfo.gender == 2\">\r\n\t\t\t\t\t\t\t\t女\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"item\" v-if=\"reportUserInfo.gender == 1\">\r\n\t\t\t\t\t\t\t\t男\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"base-info-item\">\r\n\t\t\t\t\t\t\t<text class=\"item-title\">本科入学年份：</text>\r\n\t\t\t\t\t\t\t<text class=\"item\">{{ reportUserInfo.joinYear }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"base-info-item  width-25\">\r\n\t\t\t\t\t\t\t<text class=\"item-title\">本科院校：</text>\r\n\t\t\t\t\t\t\t<text class=\"item\">{{ reportUserInfo.schoolName }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"base-info-item\">\r\n\t\t\t\t\t\t\t<text class=\"item-title\">学院：</text>\r\n\t\t\t\t\t\t\t<text class=\"item\">{{ reportUserInfo.collegeName }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"base-info-item\">\r\n\t\t\t\t\t\t\t<view class=\"item-title\">专业：</view>\r\n\t\t\t\t\t\t\t<view class=\"item major\">{{ reportUserInfo.majorName }}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"base-info-item  width-25\">\r\n\t\t\t\t\t\t\t<text class=\"item-title\">学员性格：</text>\r\n\t\t\t\t\t\t\t<text class=\"item\">{{ reportUserInfo.personality == 1 ? '内向' : '外向' }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"base-info-item\">\r\n\t\t\t\t\t\t\t<text class=\"item-title\">毕业发展：</text>\r\n\t\t\t\t\t\t\t<text class=\"item\">{{ reportUserInfo.postGraduationLabel }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\r\n\t\t\t\t\t<view class=\"hobby\">\r\n\t\t\t\t\t\t<view class=\"hobby-item\">\r\n\t\t\t\t\t\t\t<text class=\"hobby-title\">体育特长：</text>\r\n\t\t\t\t\t\t\t<text class=\"hobby-info\">{{ reportUserInfo.sportsInterest }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"hobby-item\">\r\n\t\t\t\t\t\t\t<text class=\"hobby-title\">艺术特长：</text>\r\n\t\t\t\t\t\t\t<text class=\"hobby-info\">{{ reportUserInfo.artInterest }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"hobby-item\">\r\n\t\t\t\t\t\t\t<text class=\"hobby-title\">其它特长：</text>\r\n\t\t\t\t\t\t\t<text class=\"hobby-info\">{{ reportUserInfo.academicInterest }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"hobby-item\">\r\n\t\t\t\t\t\t\t<text class=\"hobby-title\">综合描述：</text>\r\n\t\t\t\t\t\t\t<text class=\"hobby-info\">{{ reportUserInfo.collegePlan }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t</view>\r\n\r\n\r\n\r\n\t\t\t\t<!-- 高考信息 -->\r\n\t\t\t\t<view class=\"exam-info\">\r\n\t\t\t\t\t<view class=\"base-info\">\r\n\t\t\t\t\t\t<blue-title :title=\"'第二部分：高考基础信息'\"></blue-title>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view class=\"table\">\r\n\t\t\t\t\t\t<view class=\"header\">\r\n\t\t\t\t\t\t\t<view class=\"title\">\r\n\t\t\t\t\t\t\t\t总分\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"title\">\r\n\t\t\t\t\t\t\t\t排名\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"title\">\r\n\t\t\t\t\t\t\t\t位次\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"title\">\r\n\t\t\t\t\t\t\t\t语文\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"title\">\r\n\t\t\t\t\t\t\t\t数学\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"title\">\r\n\t\t\t\t\t\t\t\t外语\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"title\">\r\n\t\t\t\t\t\t\t\t物理\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"title\">\r\n\t\t\t\t\t\t\t\t化学\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"title\">\r\n\t\t\t\t\t\t\t\t生物\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"title\">\r\n\t\t\t\t\t\t\t\t政治\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"title\">\r\n\t\t\t\t\t\t\t\t历史\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"title\">\r\n\t\t\t\t\t\t\t\t地理\r\n\t\t\t\t\t\t\t</view>\r\n\r\n\r\n\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"table-line\">\r\n\t\t\t\t\t\t\t<view class=\"table-line-item\">\r\n\t\t\t\t\t\t\t\t{{ reportUserInfo.totalScore }}\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"table-line-item\">\r\n\t\t\t\t\t\t\t\t{{ reportUserInfo.rank }}\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"table-line-item\">\r\n\t\t\t\t\t\t\t\t{{ reportUserInfo.position }}\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"table-line-item\">\r\n\t\t\t\t\t\t\t\t{{ reportUserInfo.chineseScore }}\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"table-line-item\">\r\n\t\t\t\t\t\t\t\t{{ reportUserInfo.mathScore }}\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"table-line-item\">\r\n\t\t\t\t\t\t\t\t{{ reportUserInfo.foreignLangScore }}\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"table-line-item\">\r\n\t\t\t\t\t\t\t\t{{ reportUserInfo.physicsScore ? reportUserInfo.physicsScore : '-' }}\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"table-line-item\">\r\n\t\t\t\t\t\t\t\t{{ reportUserInfo.chemistryScore ? reportUserInfo.chemistryScore : '-' }}\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"table-line-item\">\r\n\t\t\t\t\t\t\t\t{{ reportUserInfo.biologyScore ? reportUserInfo.biologyScore : '-' }}\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"table-line-item\">\r\n\t\t\t\t\t\t\t\t{{ reportUserInfo.politicsScore ? reportUserInfo.politicsScore : '-' }}\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"table-line-item\">\r\n\t\t\t\t\t\t\t\t{{ reportUserInfo.historyScore ? reportUserInfo.historyScore : '-' }}\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"table-line-item\">\r\n\t\t\t\t\t\t\t\t{{ reportUserInfo.geographyScore ? reportUserInfo.geographyScore : '-' }}\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t</view>\r\n\r\n\r\n\r\n\t\t\t\t<!-- 本科院校信息 -->\r\n\t\t\t\t<view class=\"university-info\" v-if=\"reportInfo.school\">\r\n\t\t\t\t\t<blue-title :title=\"'第三部分：院校基本信息'\"></blue-title>\r\n\t\t\t\t\t<view class=\"university-tag\">\r\n\t\t\t\t\t\t<image class=\"logo\" :src=\"reportInfo.school.logo\" mode=\"\"></image>\r\n\t\t\t\t\t\t<view class=\"tag\">\r\n\t\t\t\t\t\t\t<text class=\"name\">\r\n\t\t\t\t\t\t\t\t{{ reportInfo.school.name }}\r\n\t\t\t\t\t\t\t</text>\r\n\t\t\t\t\t\t\t<view class=\"tag-list\">\r\n\t\t\t\t\t\t\t\t<view class=\"tag-list-item\" v-for=\"(tag, index) in reportInfo.school.tags\" :key=\"index\">\r\n\t\t\t\t\t\t\t\t\t{{ tag }}\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<report-content :msgtype=\"schoolInfo\"></report-content>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- 就业方向 -->\r\n\t\t\t\t<view class=\"plan\">\r\n\t\t\t\t\t<blue-title :title=\"'第四部分：就业方向'\"></blue-title>\r\n\t\t\t\t\t<report-content style='margin-top: 28rpx;' :msgtype=\"careerDirection\"></report-content>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- 升学规划 -->\r\n\t\t\t\t<view class=\"plan\">\r\n\t\t\t\t\t<blue-title :title=\"'第五部分：升学规划'\"></blue-title>\r\n\t\t\t\t\t<report-content style='margin-top: 28rpx;' :msgtype=\"educationPlanning\"></report-content>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"ability\">\r\n\t\t\t\t\t<blue-title :title=\"'第六部分：学术能力提升'\"></blue-title>\r\n\t\t\t\t\t<report-content style='margin-top: 28rpx;' :msgtype=\"academicAbilityEnhancement\"></report-content>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"university-plan\">\r\n\t\t\t\t\t<blue-title :title=\"'第七部分：大学规划'\"></blue-title>\r\n\t\t\t\t\t<report-content-two :list=\"planningList\" :zzsh_zonghe=\"zzsh_zonghe\"\r\n\t\t\t\t\t\t:jyzb_zonghe=\"jyzb_zonghe\"></report-content-two>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 预科建议 -->\r\n\t\t\t\t<view class=\"ability\">\r\n\t\t\t\t\t<blue-title :title=\"'第八部分：预科推荐'\"></blue-title>\r\n\t\t\t\t\t<report-content style='margin-top: 28rpx;' :msgtype=\"adviceList\"></report-content>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</template>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport blueTitle from \"@/components/blue_title.vue\"\r\n\timport reportContent from \"@/components/report_content.vue\"\r\n\timport reportContentTwo from \"@/components/report_content_two.vue\"\r\n\r\n\timport {\r\n\t\tgetReport,\r\n\t\tgetUserInfo\r\n\t} from \"@/api/user.js\"\r\n\t// import {\r\n\t// \tgetUserInfo,\r\n\t// \tprepareReport\r\n\t// } from \"@/api/user.js\"\r\n\timport {\r\n\t\tgetToken,\r\n\t} from \"@/utils/storage.js\";\r\n\timport {\r\n\t\tmapState\r\n\t} from \"vuex\"\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tzzsh_zonghe: '',\r\n\t\t\t\tjyzb_zonghe: '',\r\n\t\t\t\tschoolInfo: [{\r\n\t\t\t\t\t\ttype: 'schoolInfo',\r\n\t\t\t\t\t\tlabel: '学校简介',\r\n\t\t\t\t\t\tcontent: ''\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttype: 'majorInfo',\r\n\t\t\t\t\t\tlabel: '专业简介',\r\n\t\t\t\t\t\tcontent: ''\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttype: 'majorPeople',\r\n\t\t\t\t\t\tlabel: '专业招生人数',\r\n\t\t\t\t\t\tcontent: ''\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttype: 'byl',\r\n\t\t\t\t\t\tlabel: '专业保研率',\r\n\t\t\t\t\t\tcontent: ''\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttype: 'kaoyan',\r\n\t\t\t\t\t\tlabel: '专业考研情况',\r\n\t\t\t\t\t\tcontent: ''\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\r\n\t\t\t\t//就业方向\r\n\t\t\t\tcareerDirection: [{\r\n\t\t\t\t\t\ttype: 'bk_jiuye',\r\n\t\t\t\t\t\tlabel: '专业本科就业路径'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttype: 'ss_jiuye',\r\n\t\t\t\t\t\tlabel: '专业硕士就业路径'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttype: 'bs_jiuye',\r\n\t\t\t\t\t\tlabel: '专业博士就业路径'\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t\teducationPlanning: [{\r\n\t\t\t\t\t\ttype: 'gh_zzy',\r\n\t\t\t\t\t\tlabel: '转专业'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttype: 'gh_baoyan',\r\n\t\t\t\t\t\tlabel: '保研'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttype: 'gh_kaoyan',\r\n\t\t\t\t\t\tlabel: '考研'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttype: 'gh_liuxue',\r\n\t\t\t\t\t\tlabel: '留学'\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t\tacademicAbilityEnhancement: [{\r\n\t\t\t\t\t\ttype: 'ts_z_js',\r\n\t\t\t\t\t\tlabel: '专业相关竞赛'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttype: 'ts_n_js',\r\n\t\t\t\t\t\tlabel: '非专业相关竞赛'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttype: 'ts_z_ky',\r\n\t\t\t\t\t\tlabel: '专业相关科研'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttype: 'ts_n_ky',\r\n\t\t\t\t\t\tlabel: '可跨专业的相关科研'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttype: 'ts_z_zs',\r\n\t\t\t\t\t\tlabel: '专业相关证书'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttype: 'ts_n_zs',\r\n\t\t\t\t\t\tlabel: '非专业相关证书'\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t\tuniversityPlanning: [\r\n\r\n\t\t\t\t],\r\n\t\t\t\torganizationalLife: [{\r\n\t\t\t\t\t\ttype: 'zzsh_sport',\r\n\t\t\t\t\t\tlabel: '体育特长',\r\n\t\t\t\t\t\tkey: 'sportsInterest'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttype: 'zzsh_art',\r\n\t\t\t\t\t\tlabel: '艺术特长',\r\n\t\t\t\t\t\tkey: 'artInterest'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttype: 'zzsh_qita',\r\n\t\t\t\t\t\tlabel: '其他特长',\r\n\t\t\t\t\t\tkey: 'academicInterest'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttype: 'zzsh_zonghe',\r\n\t\t\t\t\t\tlabel: '综合描述',\r\n\t\t\t\t\t\tkey: 'collegePlan'\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t\tadviceList: [],\r\n\t\t\t\treportData: {\r\n\t\t\t\t\tdone: true\r\n\t\t\t\t},\r\n\t\t\t\tplanningList: [{\r\n\t\t\t\t\ttitle: \"大一期间核心学业规划\",\r\n\t\t\t\t\tcontent: {\r\n\t\t\t\t\t\tzzsh: {\r\n\t\t\t\t\t\t\ttitle: '组织生活',\r\n\t\t\t\t\t\t\tlist: []\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\txygh: {\r\n\t\t\t\t\t\t\ttitle: '学业规划',\r\n\t\t\t\t\t\t\tlist: []\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tsxbk: {\r\n\t\t\t\t\t\t\ttitle: '升学准备',\r\n\t\t\t\t\t\t\tlist: []\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tjyzb: {\r\n\t\t\t\t\t\t\ttitle: '就业指导',\r\n\t\t\t\t\t\t\tlist: []\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}],\r\n\t\t\t\tdata: {},\r\n\t\t\t\tfrom: 0, //0：客户录入  1：激活码注册\r\n\t\t\t\treportStatus: 0, //0: 未激活   1：服务已激活  2. 服务已使用\r\n\t\t\t};\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\t...mapState('user', ['reportInfo', 'reportUserInfo'])\r\n\t\t},\r\n\t\tcomponents: {\r\n\t\t\tblueTitle,\r\n\t\t\treportContent,\r\n\t\t\treportContentTwo\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\tconst userInfo = uni.getStorageSync('user') || {};\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\tthis.getReportData()\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 获取学员信息\r\n\t\t\tasync getUserDetails() {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst res = await getUserInfo()\r\n\t\t\t\t\tif (res.errCode === 0) {\r\n\t\t\t\t\t\tthis.data = res.data\r\n\t\t\t\t\t\tthis.from = res.data.from\r\n\t\t\t\t\t\t// 如果等于1代表使用激活码激活已经生成报告，没保存，跳转到生成报告页面\r\n\t\t\t\t\t\tif (res.data.reportStatus == 1) {\r\n\t\t\t\t\t\t\tthis.getReports()\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthis.getReportData()\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.tip(res.msg)\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (e) {\r\n\t\t\t\t\tuni.removeStorageSync('USER-TOKEN')\r\n\t\t\t\t\tthis.$store.commit('clearToken');\r\n\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tasync getReports() {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst result = await prepareReport(this.data)\r\n\t\t\t\t\tif (result.errCode == 0) {\r\n\t\t\t\t\t\t//记录报告信息\r\n\t\t\t\t\t\tthis.$store.commit('user/setReportInfo', result.data);\r\n\t\t\t\t\t\t//记录报告的用户信息\r\n\t\t\t\t\t\t// this.$store.commit('user/setReportUserInfo', this.stuInfo);\r\n\t\t\t\t\t\tthis.$store.commit('user/setReportUserInfo', this.data);\r\n\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\turl: '/subpkg/plan/plan'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.tip(result.msg)\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t} catch (e) {\r\n\t\t\t\t\tconsole.error(e)\r\n\t\t\t\t} finally {\r\n\t\t\t\t\tthis.showAnimation = false\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tasync getReportData() {\r\n\t\t\t\t// if (!getToken()) {\r\n\r\n\t\t\t\t// \tuni.navigateTo({\r\n\t\t\t\t// \t\turl: '/subpkg/login/login'\r\n\t\t\t\t// \t})\r\n\t\t\t\t// \treturn\r\n\t\t\t\t// }\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst res = await getReport()\r\n\t\t\t\t\tif (res.errCode === 0) {\r\n\t\t\t\t\t\t// 检查是否有报告数据\r\n\t\t\t\t\t\t// if (!res.data || !res.data.school) {\r\n\t\t\t\t\t\t// \tthis.reportData.done = false\r\n\t\t\t\t\t\t// \treturn\r\n\t\t\t\t\t\t// }\r\n\t\t\t\t\t\tthis.reportData.done = res.data.id ? true : false\r\n\t\t\t\t\t\tif (!res.data.id) {\r\n\t\t\t\t\t\t\treturn\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t// 处理基础用户信息\r\n\t\t\t\t\t\tconst userInfo = {\r\n\t\t\t\t\t\t\tname: res.data.name,\r\n\t\t\t\t\t\t\tgender: res.data.gender,\r\n\t\t\t\t\t\t\tjoinYear: res.data.joinYear,\r\n\t\t\t\t\t\t\tschoolName: res.data.schoolName,\r\n\t\t\t\t\t\t\tcollegeName: res.data.collegeName,\r\n\t\t\t\t\t\t\tmajorName: res.data.majorName,\r\n\t\t\t\t\t\t\tpersonality: res.data.personality,\r\n\t\t\t\t\t\t\tpostGraduationLabel: this.getPostGraduationLabel(res.data.postGraduation),\r\n\t\t\t\t\t\t\tsportsInterest: res.data.sportsInterest || '',\r\n\t\t\t\t\t\t\tartInterest: res.data.artInterest || '',\r\n\t\t\t\t\t\t\tacademicInterest: res.data.academicInterest || '',\r\n\t\t\t\t\t\t\tcollegePlan: res.data.collegePlan || '',\r\n\t\t\t\t\t\t\t// 高考成绩信息\r\n\t\t\t\t\t\t\ttotalScore: res.data.totalScore,\r\n\t\t\t\t\t\t\trank: res.data.rank,\r\n\t\t\t\t\t\t\tposition: res.data.position,\r\n\t\t\t\t\t\t\tchineseScore: res.data.chineseScore,\r\n\t\t\t\t\t\t\tmathScore: res.data.mathScore,\r\n\t\t\t\t\t\t\tforeignLangScore: res.data.foreignLangScore,\r\n\t\t\t\t\t\t\tphysicsScore: res.data.physicsScore,\r\n\t\t\t\t\t\t\tchemistryScore: res.data.chemistryScore,\r\n\t\t\t\t\t\t\tbiologyScore: res.data.biologyScore,\r\n\t\t\t\t\t\t\tpoliticsScore: res.data.politicsScore,\r\n\t\t\t\t\t\t\thistoryScore: res.data.historyScore,\r\n\t\t\t\t\t\t\tgeographyScore: res.data.geographyScore\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t// 处理学校信息\r\n\t\t\t\t\t\tconst schoolInfo = {\r\n\t\t\t\t\t\t\tschool: {\r\n\t\t\t\t\t\t\t\tlogo: res.data.school?.logo || '',\r\n\t\t\t\t\t\t\t\tname: res.data.school?.name || '',\r\n\t\t\t\t\t\t\t\ttags: res.data.school?.tags || []\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t// 更新学校相关信息内容\r\n\t\t\t\t\t\tthis.schoolInfo = this.schoolInfo.map(item => ({\r\n\t\t\t\t\t\t\t...item,\r\n\t\t\t\t\t\t\tcontent: res.data[item.type] || ''\r\n\t\t\t\t\t\t}))\r\n\r\n\t\t\t\t\t\t// 更新就业方向内容\r\n\t\t\t\t\t\tthis.careerDirection = this.careerDirection.map(item => ({\r\n\t\t\t\t\t\t\t...item,\r\n\t\t\t\t\t\t\tcontent: res.data[item.type] || ''\r\n\t\t\t\t\t\t}))\r\n\r\n\t\t\t\t\t\t// 更新升学规划内容\r\n\t\t\t\t\t\tthis.educationPlanning = this.educationPlanning.map(item => ({\r\n\t\t\t\t\t\t\t...item,\r\n\t\t\t\t\t\t\tcontent: res.data[item.type] || ''\r\n\t\t\t\t\t\t}))\r\n\r\n\t\t\t\t\t\t// 更新学术能力提升内容\r\n\t\t\t\t\t\tthis.academicAbilityEnhancement = this.academicAbilityEnhancement.map(item => ({\r\n\t\t\t\t\t\t\t...item,\r\n\t\t\t\t\t\t\tcontent: res.data[item.type] || ''\r\n\t\t\t\t\t\t}))\r\n\r\n\t\t\t\t\t\t// 处理预科建议数据\r\n\t\t\t\t\t\tif (res.data.advice && Array.isArray(res.data.advice)) {\r\n\t\t\t\t\t\t\tthis.adviceList = res.data.advice.map(item => ({\r\n\t\t\t\t\t\t\t\ttype: item.id,\r\n\t\t\t\t\t\t\t\tlabel: item.name,\r\n\t\t\t\t\t\t\t\tcontent: item.content\r\n\t\t\t\t\t\t\t}))\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t// 更新大学规划内容\r\n\t\t\t\t\t\tif (res.data.grade_1) {\r\n\t\t\t\t\t\t\tconst grade1 = res.data.grade_1\r\n\t\t\t\t\t\t\tObject.keys(grade1).forEach(key => {\r\n\t\t\t\t\t\t\t\tif (this.planningList[0].content[key]) {\r\n\t\t\t\t\t\t\t\t\tthis.planningList[0].content[key].list = grade1[key]\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tObject.keys(res.data).forEach(key => {\r\n\t\t\t\t\t\t\t\tif (this.planningList[0].content[key]) {\r\n\t\t\t\t\t\t\t\t\tthis.planningList[0].content[key].list = res.data[key]\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (res.data.zzsh_zonghe) {\r\n\t\t\t\t\t\t\tthis.planningList[0].content.zzsh.list.push({\r\n\t\t\t\t\t\t\t\tname: \"综合描述推荐\",\r\n\t\t\t\t\t\t\t\tcontent: res.data.zzsh_zonghe\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (res.data.jyzb_zonghe) {\r\n\t\t\t\t\t\t\tthis.planningList[0].content.jyzb.list.push({\r\n\t\t\t\t\t\t\t\tname: \"综合描述推荐\",\r\n\t\t\t\t\t\t\t\tcontent: res.data.jyzb_zonghe\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t// 存储到 vuex\r\n\t\t\t\t\t\tthis.$store.commit('user/setReportUserInfo', userInfo)\r\n\t\t\t\t\t\tthis.$store.commit('user/setReportInfo', schoolInfo)\r\n\r\n\t\t\t\t\t\tthis.reportData.done = true\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (e) {\r\n\t\t\t\t\tconsole.error('获取报告失败:', e)\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '获取报告失败',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t\tthis.reportData.done = false\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 处理毕业发展选项\r\n\t\t\tgetPostGraduationLabel(postGraduation) {\r\n\t\t\t\tif (!postGraduation) return ''\r\n\t\t\t\tconst labels = []\r\n\t\t\t\tif (postGraduation.includes('1')) labels.push('考研')\r\n\t\t\t\tif (postGraduation.includes('2')) labels.push('保研')\r\n\t\t\t\tif (postGraduation.includes('3')) labels.push('就业')\r\n\t\t\t\tif (postGraduation.includes('4')) labels.push('留学')\r\n\t\t\t\treturn labels.join('、')\r\n\t\t\t},\r\n\t\t\t// 返回上一页\r\n\t\t\tback() {\r\n\t\t\t\tconst pages = getCurrentPages(); // 获取页面栈\r\n\t\t\t\tif (pages.length < 2) {\r\n\t\t\t\t\tuni.switchTab({\r\n\t\t\t\t\t\turl: '/pages/admission_report/admission_report'\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tconst prevPage = pages[pages.length - 2]; // 上一个页面实例\r\n\t\t\t\tconst prevRoute = prevPage.route; // 上一个页面的路径，如 \"/pages/home/<USER>\"\r\n\t\t\t\tuni.switchTab({\r\n\t\t\t\t\turl: '/' + prevRoute\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.container {\r\n\t\tpadding-bottom: 20px;\r\n\t}\r\n\r\n\t.back {\r\n\t\tposition: absolute;\r\n\t\ttop: 30rpx;\r\n\t\tleft: 30rpx;\r\n\t}\r\n\r\n\t.header {\r\n\r\n\t\t.header-img {\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 1070rpx;\r\n\t\t}\r\n\r\n\t\t.teacher-info {\r\n\t\t\tposition: absolute;\r\n\t\t\ttop: 396rpx;\r\n\t\t\tleft: 520rpx;\r\n\t\t\twidth: 200rpx;\r\n\t\t\theight: 50rpx;\r\n\t\t\tline-height: 50rpx;\r\n\t\t\ttext-align: center;\r\n\t\t\tcolor: #000;\r\n\t\t}\r\n\t}\r\n\r\n\t.content {\r\n\t\tpadding-top: 28rpx;\r\n\t\tpadding: 0 30rpx;\r\n\r\n\t\t.base-info-item-container {\r\n\t\t\twidth: 100%;\r\n\t\t\tmargin-top: 18rpx;\r\n\t\t\tfont-size: $primary-font-size;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-content: space-between;\r\n\t\t\tflex-wrap: wrap;\r\n\t\t\tjustify-content: flex-start;\r\n\r\n\t\t\t.base-info-item {\r\n\t\t\t\twidth: 33%;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: start !important;\r\n\t\t\t\tmargin-right: 10rpx;\r\n\t\t\t\tjustify-content: flex-start;\r\n\r\n\t\t\t\t::v-deep .uni-body {\r\n\t\t\t\t\tline-height: 0 !important;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t/* 显示省略号 */\r\n\t\t\t\t.item-title {\r\n\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\tfont-size: $primary-font-size;\r\n\t\t\t\t\tcolor: #5A5A5A;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.item {\r\n\t\t\t\t\tcolor: #5A5A5A;\r\n\r\n\t\t\t\t}\r\n\r\n\t\t\t\t::v-deep .major {\r\n\t\t\t\t\twidth: 80%;\r\n\t\t\t\t\tdisplay: -webkit-box;\r\n\t\t\t\t\t-webkit-line-clamp: 2;\r\n\t\t\t\t\t/* 限制显示行数 */\r\n\t\t\t\t\t-webkit-box-orient: vertical;\r\n\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\ttext-overflow: ellipsis;\r\n\t\t\t\t\tline-height: 25rpx !important;\r\n\t\t\t\t\tmargin-top: 10rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.wrapItem {\r\n\t\t\t\t\twidth: 80%;\r\n\t\t\t\t\twhite-space: nowrap;\r\n\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\t/* 隐藏溢出内容 */\r\n\t\t\t\t\ttext-overflow: ellipsis;\r\n\t\t\t\t}\r\n\r\n\t\t\t}\r\n\r\n\t\t\t.width-25 {\r\n\t\t\t\twidth: 28%;\r\n\t\t\t}\r\n\r\n\r\n\t\t}\r\n\r\n\t\t/*爱好*/\r\n\t\t.hobby {\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-direction: column;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: flex-start;\r\n\r\n\t\t}\r\n\r\n\t\t.hobby-item {\r\n\t\t\twidth: 100%;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: flex-start;\r\n\t\t\tborder: 1rpx solid #A0E4C4;\r\n\t\t\tborder-radius: 4rpx;\r\n\t\t\tpadding: 8rpx 12rpx;\r\n\t\t\tmargin-top: 18rpx;\r\n\t\t}\r\n\r\n\t\t.hobby-title {\r\n\t\t\tfont-weight: bold;\r\n\t\t\tfont-size: $primary-font-size;\r\n\t\t\tcolor: #5A5A5A;\r\n\t\t\twidth: 100rpx;\r\n\t\t}\r\n\r\n\t\t.hobby-info {\r\n\t\t\tfont-weight: 400;\r\n\t\t\tfont-size: $primary-font-size;\r\n\t\t\tcolor: #5A5A5A;\r\n\t\t\tmargin-left: 14rpx;\r\n\t\t\twidth: 80%;\r\n\t\t}\r\n\r\n\r\n\t\t.exam-info {\r\n\t\t\tmargin-top: 20rpx;\r\n\r\n\t\t\t.table {\r\n\t\t\t\tmargin-top: 20rpx;\r\n\t\t\t\theight: 48rpx;\r\n\t\t\t\tborder-radius: 8rpx;\r\n\t\t\t\tborder: 1rpx solid #1BB394;\r\n\t\t\t\tfont-size: 12rpx;\r\n\r\n\t\t\t\t.header,\r\n\t\t\t\t.table-line {\r\n\t\t\t\t\theight: 24rpx;\r\n\t\t\t\t\tborder-bottom: 1rpx solid #1BB394;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: flex-start;\r\n\t\t\t\t\talign-items: center;\r\n\r\n\t\t\t\t\t.title,\r\n\t\t\t\t\t.table-line-item {\r\n\t\t\t\t\t\theight: 24rpx;\r\n\t\t\t\t\t\tline-height: 24rpx;\r\n\t\t\t\t\t\tflex: 1;\r\n\t\t\t\t\t\tborder-right: 1rpx solid #1BB394;\r\n\t\t\t\t\t\ttext-align: center;\r\n\r\n\t\t\t\t\t\t&:last-child {\r\n\t\t\t\t\t\t\tborder: 0;\r\n\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.table-line {\r\n\t\t\t\t\tborder: 0;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\r\n\t\t.university-info {\r\n\t\t\tmargin-top: 32rpx;\r\n\r\n\t\t\t.university-tag {\r\n\t\t\t\tmargin-top: 18rpx;\r\n\t\t\t\tmargin-bottom: 32rpx;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: flex-start;\r\n\r\n\t\t\t\t.logo {\r\n\t\t\t\t\twidth: 70rpx;\r\n\t\t\t\t\theight: 70rpx;\r\n\t\t\t\t\tmargin-right: 24rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.tag {\r\n\t\t\t\t\tflex: 1;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tflex-direction: column;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tjustify-content: flex-start;\r\n\r\n\t\t\t\t\t.name {\r\n\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\t\t\tcolor: #5A5A5A;\r\n\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\ttext-align: left;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.tag-list {\r\n\t\t\t\t\t\tmargin-top: 10rpx;\r\n\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\t// justify-content: space-between;\r\n\r\n\t\t\t\t\t\t.tag-list-item {\r\n\t\t\t\t\t\t\twidth: 68rpx;\r\n\t\t\t\t\t\t\theight: 30rpx;\r\n\t\t\t\t\t\t\tbackground: #FFB975;\r\n\t\t\t\t\t\t\tborder-radius: 6rpx;\r\n\t\t\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\t\t\tfont-size: 14rpx;\r\n\t\t\t\t\t\t\tcolor: #FFFFFF;\r\n\t\t\t\t\t\t\tline-height: 30rpx;\r\n\t\t\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\t\t\tmargin-right: 15rpx;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t/* 升学规划*/\r\n\t\t.plan,\r\n\t\t.ability,\r\n\t\t.university-plan {\r\n\t\t\tmargin-top: 32rpx;\r\n\r\n\t\t}\r\n\t}\r\n\r\n\t.no-report {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tjustify-content: flex-start;\r\n\r\n\t\t.no-data-img {\r\n\t\t\twidth: 360rpx;\r\n\t\t\tmargin-bottom: 40rpx;\r\n\t\t}\r\n\r\n\t\t.no-data-tip {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tflex-direction: column;\r\n\t\t\tjustify-content: center;\r\n\t\t\tgap: 44rpx;\r\n\t\t}\r\n\r\n\t\t.no-data-text {\r\n\t\t\tfont-size: 32rpx;\r\n\t\t\tcolor: #999;\r\n\t\t\tfont-weight: 500;\r\n\t\t}\r\n\t}\r\n\r\n\t/* 添加头部导航栏样式 */\r\n\t.header {\r\n\t\tbackground-color: #fff;\r\n\r\n\t\t.nav-bar {\r\n\t\t\twidth: 100vw;\r\n\t\t\theight: 88rpx;\r\n\t\t\tpadding: 0 30rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tposition: relative;\r\n\t\t\tbackground-color: #1BB394;\r\n\t\t\tcolor: #fff;\r\n\r\n\t\t\t.back-icon {\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tleft: 30rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.title {\r\n\t\t\t\tflex: 1;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tfont-weight: 500;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.headers {\r\n\t\tposition: fixed;\r\n\t\tbackground-color: transparent !important;\r\n\r\n\t\t.nav-bar {\r\n\t\t\twidth: 100vw;\r\n\t\t\theight: 88rpx;\r\n\t\t\tpadding: 0 30rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tposition: relative;\r\n\t\t\tbackground-color: transparent !important;\r\n\t\t\tcolor: #888;\r\n\t\t}\r\n\t}\r\n\r\n\tuni-button:after {\r\n\t\tcontent: none !important;\r\n\t\tborder: none !important;\r\n\t}\r\n\r\n\t.transparent-button {\r\n\t\tposition: absolute;\r\n\t\ttop: 30rpx;\r\n\t\tleft: 30rpx;\r\n\t\tz-index: 99999;\r\n\t\tpadding: 0;\r\n\t\tmargin: 0;\r\n\t\tbackground: transparent;\r\n\t\tborder: none;\r\n\t\toutline: none;\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./report_view.vue?vue&type=style&index=0&id=475f855d&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./report_view.vue?vue&type=style&index=0&id=475f855d&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754557255169\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}