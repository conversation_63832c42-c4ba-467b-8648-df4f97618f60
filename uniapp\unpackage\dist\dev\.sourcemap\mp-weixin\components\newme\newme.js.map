{"version": 3, "sources": ["webpack:///G:/code/uniapp/daxuetonguni/uniapp/components/newme/newme.vue?a0d3", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/components/newme/newme.vue?3075", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/components/newme/newme.vue?0cd1", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/components/newme/newme.vue?fca6", "uni-app:///components/newme/newme.vue", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/components/newme/newme.vue?87c5", "webpack:///G:/code/uniapp/daxuetonguni/uniapp/components/newme/newme.vue?89e6"], "names": ["data", "enter", "token", "user", "percentage", "asset", "mounted", "console", "methods", "routergo", "uni", "url", "closepage"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,4NAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAA+lB,CAAgB,ynBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACyOnnB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAGA;EACA;EACAA;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACAC;IACA;EACA;;EAEAC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IAEA;IACA;IACA;IACAC;MAEA;MACA;MACA;QACA;UACAC;YACAC;UACA;QACA;MAEA;QACA;QACAJ;MACA;IAEA;IACAK;MACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACpSA;AAAA;AAAA;AAAA;AAAkqC,CAAgB,woCAAG,EAAC,C;;;;;;;;;;;ACAtrC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/newme/newme.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./newme.vue?vue&type=template&id=70684b36&scoped=true&\"\nvar renderjs\nimport script from \"./newme.vue?vue&type=script&lang=js&\"\nexport * from \"./newme.vue?vue&type=script&lang=js&\"\nimport style0 from \"./newme.vue?vue&type=style&index=0&id=70684b36&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"70684b36\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/newme/newme.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./newme.vue?vue&type=template&id=70684b36&scoped=true&\"", "var components\ntry {\n  components = {\n    login: function () {\n      return import(\n        /* webpackChunkName: \"components/login/login\" */ \"@/components/login/login.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./newme.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./newme.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"content\">\r\n\t\t<view class=\"content_hadrimg\">\r\n\t\t\t<image src=\"https://yanqu-shengxue-1300870289.cos.ap-nanjing.myqcloud.com/preset/dxt_mini/icon.png\" mode=\"\">\r\n\t\t\t</image>\r\n\t\t</view>\r\n\t\t<view class=\"content_main\">\r\n\t\t\t<view class=\"content_main_box\">\r\n\t\t\t\t<view class=\"content_main_box_1\">\r\n\t\t\t\t\t<!-- '/pages/me_all/personage/personage' -->\r\n\t\t\t\t\t<view class=\"content_main_box_1_left\" @tap=\"routergo()\">\r\n\t\t\t\t\t\t<view class=\"content_main_box_1_left_avatar\">\r\n\t\t\t\t\t\t\t<view class=\"content_main_box_1_left_avatar_img\">\r\n\t\t\t\t\t\t\t\t<image class=\"\" src=\"../../static/me/icon1.svg\" mode=\"\"></image>\r\n\t\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t\t<view class=\"content_main_box_1_left_img\">\r\n\t\t\t\t\t\t\t\t<image :src=\"user.avatar || '../../static/avatar.png'\" mode=\"aspectFill\"></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<!-- <view class=\"content_main_box_1_left_LV\" v-if=\"user.level\">LV{{user.level||0}}</view> -->\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"content_main_box_1_left_title flexColumn\">\r\n\t\t\t\t\t\t\t<view class=\"content_main_box_1_left_title_1 flexc\">\r\n\t\t\t\t\t\t\t\t<view class=\"me-text-beyond content_main_box_1_left_title_1_text\">\r\n\t\t\t\t\t\t\t\t\t{{user.nickname||'请登录'}}\r\n\t\t\t\t\t\t\t\t</view>\r\n\r\n\r\n\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<!-- <view class=\"content_main_box_1_left_title_1_btn\" v-if=\"token\">\r\n\t\t\t\t\t\t\t\t编辑\r\n\t\t\t\t\t\t\t</view> -->\r\n\t\t\t\t\t\t\t<!-- <view class=\"content_main_box_1_left_title_2\">\r\n\t\t\t\t\t\t\t\t再获得{{user.upgrade||0}}个积分可升级为LV{{user.level+1||0}}\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"content_main_box_1_left_title_3 flexc\">\r\n\t\t\t\t\t\t\t\t<view class=\"content_main_box_1_left_title_3_line\">\r\n\t\t\t\t\t\t\t\t\t<u-line-progress :percentage=\"percentage\" :showText=\"false\" activeColor=\"#05B6F6\"\r\n\t\t\t\t\t\t\t\t\t\theight='8'></u-line-progress>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"\" style=\"height: 8rpx;\">\r\n\t\t\t\t\t\t\t\t\t{{user.score||0}}/{{user.upgrade||0}}\r\n\t\t\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t\t</view> -->\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- <view class=\"content_main_box_1_right\" @tap=\"routergo('/pages/me_all/codeqr/codeqr')\">\r\n\t\t\t\t\t\t<image src=\"@/static/code.svg\" mode=\"\"></image>\r\n\t\t\t\t\t</view> -->\r\n\t\t\t\t</view>\r\n\r\n\r\n\t\t\t</view>\r\n\t\t\t<!-- <height :hg='!user.openid?100:10'></height> -->\r\n\t\t\t<view class=\"card-box\">\r\n\t\t\t\t<view class=\"card-box_title\">\r\n\t\t\t\t\t我的资产\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"card-box_title_content flexc flexs\">\r\n\t\t\t\t\t<view class=\"card-box_title_content_item\" @tap=\"routergo('/pages/me_all/integral/integral')\">\r\n\t\t\t\t\t\t<view class=\"card-box_title_content_item_top flexc\">\r\n\r\n\t\t\t\t\t\t\t<image class=\"img_1\" src=\"../../static/me/icon2.png\" mode=\"\"></image>\r\n\r\n\t\t\t\t\t\t\t<view class=\"text_1\">蝌蚪币</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"card-box_title_content_item_bom\">\r\n\t\t\t\t\t\t\t<span>{{asset.point || 0}}个</span><span>未使用</span>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"card-box_title_content_item\" @tap=\"routergo('pages/me_all/coupon/coupon')\">\r\n\t\t\t\t\t\t<view class=\"card-box_title_content_item_top flexc\">\r\n\t\t\t\t\t\t\t<image class=\"img_2\" src=\"../../static/me/icon3.svg\" mode=\"\"></image>\r\n\t\t\t\t\t\t\t<view class=\"text_1\">优惠券</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"card-box_title_content_item_bom\">\r\n\t\t\t\t\t\t\t<span>{{asset.couponCnt || 0}}张</span><span>未使用</span>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- <view class=\"card-box_title_content_item\" @tap=\"routergo('/pages/wallet/wallet')\">\r\n\t\t\t\t\t\t<view class=\"card-box_title_content_item_top flexc \">\r\n\t\t\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t\t\t\t<image class=\"img_3\" src=\"../../static/me/icon4.svg\" mode=\"\"></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"text_1\">钱包</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"card-box_title_content_item_bom\">\r\n\t\t\t\t\t\t\t<span>{{user.money || 0}}元</span><span>未使用</span>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t</view> -->\r\n\t\t\t\t</view>\r\n\r\n\r\n\r\n\t\t\t</view>\r\n\t\t\t<view class=\"card\" v-if=\"user.user\">\r\n\t\t\t\t<view class=\"card_title\">\r\n\t\t\t\t\t机构管理\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"card_groud lastflex\">\r\n\r\n\t\t\t\t\t<view class=\"card_groud_item\" @tap=\"routergo('/pages/me_all/order_off/order_off?goback='+2)\">\r\n\t\t\t\t\t\t<image src=\"../../static/me/icon18.png\" mode=\"\"></image>\r\n\t\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t\t\t扫码核验\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"card_groud_item\" @tap=\"routergo('/pages/me_all/all_orders/all_orders?goback='+2)\">\r\n\t\t\t\t\t\t<image src=\"../../static/me/icon19.png\" mode=\"\"></image>\r\n\t\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t\t\t全部订单\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"card\" v-else>\r\n\t\t\t\t<view class=\"card_title\">\r\n\t\t\t\t\t我的订单\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"card_groud flexw\">\r\n\r\n\t\t\t\t\t<view class=\"card_groud_item wrap\"\r\n\t\t\t\t\t\t@tap=\"routergo('/pages/order_all/shipping_address/shipping_address?goback='+2)\">\r\n\t\t\t\t\t\t<image src=\"../../static/me/icon12.png\" mode=\"\"></image>\r\n\t\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t\t\t已支付\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- <button open-type=\"contact\" @contact=\"token?contact:''\">\r\n\t\t\t\t\t\t<view class=\"card_groud_item\">\r\n\t\t\t\t\t\t\t<image src=\"../../static/me/icon13.png\" mode=\"\"></image>\r\n\t\t\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t\t\t\t待发货\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</button> -->\r\n\t\t\t\t\t<!-- <view class=\"card_groud_item\"\r\n\t\t\t\t\t\t@tap=\"routerTo('/pages/order_all/login_protocol/login_protocol?name='+'会员协议'+'&state='+0)\">\r\n\t\t\t\t\t\t<image src=\"../../static/me/icon8.svg\" mode=\"\"></image>\r\n\t\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t\t\t会员协议\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view> -->\r\n\t\t\t\t\t<!-- <view class=\"card_groud_item wrap\"\r\n\t\t\t\t\t\t@tap=\"routergo('/pages/me_all/messagenotification/messagenotification')\">\r\n\t\t\t\t\t\t<image src=\"../../static/me/icon14.png\" mode=\"\"></image>\r\n\t\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t\t\t待收货\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view> -->\r\n\r\n\t\t\t\t\t<view class=\"card_groud_item wrap\" @tap=\"routergo('/pages/me_all/setmessage/setmessage')\">\r\n\t\t\t\t\t\t<image src=\"../../static/me/icon15.png\" mode=\"\"></image>\r\n\t\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t\t\t待核验\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"card_groud_item wrap\"\r\n\t\t\t\t\t\t@tap=\"routergo('/pages/me_all/messagenotification/messagenotification')\">\r\n\t\t\t\t\t\t<image src=\"../../static/me/icon14.png\" mode=\"\"></image>\r\n\t\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t\t\t已退款\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- <view class=\"border\"></view> -->\r\n\t\t\t\t\t<image class=\"border\" src=\"../../static/me/icon16.png\" mode=\"\"></image>\r\n\t\t\t\t\t<view class=\"card_groud_item wrap\" @tap=\"routergo('/pages/me_all/my_orders/my_orders')\">\r\n\t\t\t\t\t\t<image src=\"../../static/me/icon17.png\" mode=\"\"></image>\r\n\t\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t\t\t全部订单\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"card\">\r\n\t\t\t\t<view class=\"card_title\">\r\n\t\t\t\t\t我的功能\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"card_groud flexw\">\r\n\r\n\t\t\t\t\t<view class=\"card_groud_item\"\r\n\t\t\t\t\t\t@tap=\"routergo('/pages/order_all/shipping_address/shipping_address?goback='+2)\">\r\n\t\t\t\t\t\t<image class=\"svgImg\" src=\"../../static/me/icon5.svg\" mode=\"\"></image>\r\n\t\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t\t\t我的地址\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- <button open-type=\"contact\" @contact=\"token?contact:''\">\r\n\t\t\t\t\t\t<view class=\"card_groud_item\">\r\n\t\t\t\t\t\t\t<image class=\"svgImg\" src=\"../../static/me/icon6.svg\" mode=\"\"></image>\r\n\t\t\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t\t\t\t官方客服\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</button> -->\r\n\t\t\t\t\t<!-- <view class=\"card_groud_item\"\r\n\t\t\t\t\t\t@tap=\"routerTo('/pages/order_all/login_protocol/login_protocol?name='+'会员协议'+'&state='+0)\">\r\n\t\t\t\t\t\t<image src=\"../../static/me/icon8.svg\" mode=\"\"></image>\r\n\t\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t\t\t会员协议\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view> -->\r\n\t\t\t\t\t<!-- <view class=\"card_groud_item\"\r\n\t\t\t\t\t\t@tap=\"routergo('/pages/me_all/messagenotification/messagenotification')\">\r\n\t\t\t\t\t\t<image class=\"svgImg\" src=\"../../static/me/icon7.svg\" mode=\"\"></image>\r\n\t\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t\t\t消息通知\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view class=\"card_groud_item\" @tap=\"routergo('/pages/me_all/setmessage/setmessage')\">\r\n\t\t\t\t\t\t<image class=\"svgImg\" src=\"../../static/me/icon10.svg\" mode=\"\"></image>\r\n\t\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t\t\t我的设置\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view> -->\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t</view>\r\n\r\n\t\t<login :show=\"enter\" @closepage='closepage'></login>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\tuserInfo\r\n\t} from \"@/api/public.js\"\r\n\texport default {\r\n\t\t// props: ['asset'],\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tenter: false,\r\n\t\t\t\ttoken: uni.getStorageSync('TOKEN') || null,\r\n\t\t\t\tuser: uni.getStorageSync('user') || {},\r\n\t\t\t\tpercentage: 0,\r\n\t\t\t\tasset: uni.getStorageSync('ASSET') || {}\r\n\t\t\t};\r\n\t\t},\r\n\r\n\t\tmounted() {\r\n\t\t\tconsole.log('this.enter', this.enter);\r\n\t\t\t// this.getUser()\r\n\t\t},\r\n\r\n\t\tmethods: {\r\n\t\t\t// async getUser() {\r\n\t\t\t// \tlet token = uni.getStorageSync('userinfo').token\r\n\t\t\t// \tif (token) {\r\n\t\t\t// \t\tlet user = await userInfo()\r\n\t\t\t// \t\tif (user.code == 1) {\r\n\t\t\t// \t\t\tuni.setStorageSync('user', user.data)\r\n\t\t\t// \t\t\tthis.percentage = (this.user.score / this.user.upgrade) * 100\r\n\t\t\t// \t\t}\r\n\t\t\t// \t}\r\n\r\n\t\t\t// },\r\n\r\n\t\t\t// contact(e) {\r\n\t\t\t// \tconsole.log(e);\r\n\t\t\t// },\r\n\t\t\troutergo(url) {\r\n\r\n\t\t\t\tlet token = uni.getStorageSync('TOKEN')\r\n\t\t\t\tlet user = uni.getStorageSync('user')\r\n\t\t\t\tif (token && user) {\r\n\t\t\t\t\tif (url) {\r\n\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\turl: url\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.enter = true\r\n\t\t\t\t\tconsole.log('243343443', this.enter);\r\n\t\t\t\t}\r\n\r\n\t\t\t},\r\n\t\t\tclosepage(data) {\r\n\t\t\t\tthis.enter = false\r\n\t\t\t\tthis.user = uni.getStorageSync('user')\r\n\t\t\t\tthis.asset = data\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\tpage {\r\n\t\tmin-height: 100vh;\r\n\t\tbackground-color: #F7F7F7 !important;\r\n\t}\r\n\r\n\t.content {\r\n\t\tposition: relative;\r\n\t\tbackground-color: #F7F7F7 !important;\r\n\r\n\t\t.content_hadrimg {\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 503rpx;\r\n\t\t\tposition: absolute;\r\n\t\t\ttop: 0;\r\n\t\t}\r\n\r\n\t\t.content_main {\r\n\t\t\tposition: absolute;\r\n\t\t\ttop: 403rpx;\r\n\t\t\tposition: relative;\r\n\t\t\tpadding-bottom: 80rpx;\r\n\r\n\t\t\t.content_main_box {\r\n\t\t\t\twidth: 703rpx;\r\n\t\t\t\tborder-radius: 16rpx 16rpx 0 0;\r\n\t\t\t\tmargin: 0 auto;\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\ttop: -200rpx;\r\n\t\t\t\tleft: 50%;\r\n\t\t\t\tbackground-color: transparent;\r\n\t\t\t\ttransform: translateX(-50%);\r\n\r\n\t\t\t\t.content_main_box_1 {\r\n\t\t\t\t\tmargin: 0 auto;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tjustify-content: space-between;\r\n\r\n\t\t\t\t\t.content_main_box_1_left {\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\tmargin-left: 10rpx;\r\n\r\n\t\t\t\t\t\t.content_main_box_1_left_avatar {\r\n\t\t\t\t\t\t\tposition: relative;\r\n\t\t\t\t\t\t\twidth: 133rpx;\r\n\t\t\t\t\t\t\theight: 133rpx;\r\n\t\t\t\t\t\t\tmargin-right: 20rpx;\r\n\r\n\t\t\t\t\t\t\t.content_main_box_1_left_avatar_img {\r\n\t\t\t\t\t\t\t\twidth: 133rpx;\r\n\t\t\t\t\t\t\t\theight: 133rpx;\r\n\t\t\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\t\t\ttop: 0;\r\n\t\t\t\t\t\t\t\tleft: 0;\r\n\t\t\t\t\t\t\t\tright: 0;\r\n\t\t\t\t\t\t\t\tbottom: 0;\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t.content_main_box_1_left_img {\r\n\t\t\t\t\t\t\t\twidth: 117rpx;\r\n\t\t\t\t\t\t\t\theight: 117rpx;\r\n\t\t\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\t\t\ttop: 50%;\r\n\t\t\t\t\t\t\t\tleft: 50%;\r\n\t\t\t\t\t\t\t\ttransform: translate(-50%, -50%);\r\n\r\n\t\t\t\t\t\t\t\timage {\r\n\t\t\t\t\t\t\t\t\tborder-radius: 50%;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t.content_main_box_1_left_LV {\r\n\t\t\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\t\t\tbottom: -16rpx;\r\n\t\t\t\t\t\t\t\tleft: 50%;\r\n\t\t\t\t\t\t\t\ttransform: translateX(-50%);\r\n\t\t\t\t\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\t\t\t\t\tcolor: #05B6F6;\r\n\t\t\t\t\t\t\t\tbackground-color: #F1FBFF;\r\n\t\t\t\t\t\t\t\tpadding: 6rpx 20rpx;\r\n\t\t\t\t\t\t\t\tbox-sizing: border-box;\r\n\t\t\t\t\t\t\t\tborder-radius: 30rpx;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\r\n\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.content_main_box_1_left_title {\r\n\t\t\t\t\t\t// margin-left: 20rpx;\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\tflex-direction: column;\r\n\t\t\t\t\t\tjustify-content: space-evenly;\r\n\r\n\t\t\t\t\t\t.content_main_box_1_left_title_1 {\r\n\t\t\t\t\t\t\t.content_main_box_1_left_title_1_text {\r\n\t\t\t\t\t\t\t\tfont-weight: 500;\r\n\t\t\t\t\t\t\t\tfont-size: 34rpx;\r\n\t\t\t\t\t\t\t\tcolor: #FFFFFF;\r\n\t\t\t\t\t\t\t\tmax-width: 300rpx;\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\ttext:nth-child(2) {\r\n\t\t\t\t\t\t\t\theight: 32rpx;\r\n\t\t\t\t\t\t\t\tbackground: #F1FBFF;\r\n\t\t\t\t\t\t\t\tborder-radius: 10rpx 10rpx 10rpx 0rpx;\r\n\t\t\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\t\t\t\tcolor: #00CCFF;\r\n\t\t\t\t\t\t\t\tpadding: 5rpx 10rpx;\r\n\t\t\t\t\t\t\t\tmargin-left: 10rpx;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t.content_main_box_1_left_title_1_btn {\r\n\t\t\t\t\t\t\twidth: 88rpx;\r\n\t\t\t\t\t\t\theight: 41rpx;\r\n\t\t\t\t\t\t\tbackground: rgba(255, 255, 255, 0.35);\r\n\t\t\t\t\t\t\tborder-radius: 424rpx 424rpx 424rpx 424rpx;\r\n\t\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\t\tcolor: #FFFFFF;\r\n\t\t\t\t\t\t\tline-height: 41rpx;\r\n\t\t\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\t\t\t// margin-left: 10rpx;\r\n\t\t\t\t\t\t\tmargin-top: 6rpx;\r\n\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t.content_main_box_1_left_title_2 {\r\n\t\t\t\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\t\t\t\tcolor: #FFFFFF;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t.content_main_box_1_left_title_3 {\r\n\t\t\t\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\t\t\t\tcolor: #FFFFFF;\r\n\r\n\t\t\t\t\t\t\t.content_main_box_1_left_title_3_line {\r\n\t\t\t\t\t\t\t\twidth: 230rpx;\r\n\t\t\t\t\t\t\t\theight: 8rpx;\r\n\t\t\t\t\t\t\t\tpadding-top: 12rpx;\r\n\t\t\t\t\t\t\t\tmargin-right: 10rpx;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.content_main_box_1_right {\r\n\t\t\t\t\twidth: 60rpx;\r\n\t\t\t\t\theight: 60rpx;\r\n\t\t\t\t\tmargin-right: 10rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.content_main_box_2 {\r\n\t\t\t\t\twidth: 645rpx;\r\n\t\t\t\t\theight: 70rpx;\r\n\t\t\t\t\tbackground: #F1FBFF;\r\n\t\t\t\t\tborder-radius: 8rpx;\r\n\t\t\t\t\tmargin: 0 auto;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tjustify-content: space-between;\r\n\r\n\t\t\t\t\t.content_main_box_2_left {\r\n\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\t\tcolor: #00C8FF;\r\n\t\t\t\t\t\tmargin-left: 10rpx;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.content_main_box_2_right {\r\n\t\t\t\t\t\twidth: 120rpx;\r\n\t\t\t\t\t\theight: 54rpx;\r\n\t\t\t\t\t\tbackground: #00CCFF;\r\n\t\t\t\t\t\tborder-radius: 27rpx;\r\n\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\t\tcolor: #F1FCFF;\r\n\t\t\t\t\t\tline-height: 54rpx;\r\n\t\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\t\tmargin-right: 10rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\r\n\t\t}\r\n\r\n\t\t.card-box {\r\n\t\t\twidth: 690rpx;\r\n\t\t\tbackground: #FFFFFF;\r\n\t\t\tborder-radius: 15rpx 15rpx 15rpx 15rpx;\r\n\t\t\tmargin: -50rpx auto 0;\r\n\t\t\tpadding: 23rpx 33rpx;\r\n\t\t\tbox-sizing: border-box;\r\n\r\n\t\t\t.card-box_title {\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\tcolor: #343434;\r\n\t\t\t\tmargin-bottom: 23rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.card-box_title_content {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: space-evenly;\r\n\r\n\t\t\t\t.card-box_title_content_item {\r\n\r\n\t\t\t\t\t.card-box_title_content_item_top {\r\n\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\t\tmargin-bottom: 15rpx;\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\tjustify-content: center;\r\n\r\n\t\t\t\t\t\t.img_1 {\r\n\t\t\t\t\t\t\twidth: 56rpx;\r\n\t\t\t\t\t\t\theight: 56rpx;\r\n\t\t\t\t\t\t\tmargin-right: 10rpx;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t.img_2 {\r\n\t\t\t\t\t\t\twidth: 58rpx;\r\n\t\t\t\t\t\t\theight: 44rpx;\r\n\t\t\t\t\t\t\tmargin-right: 10rpx;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t.img_3 {\r\n\t\t\t\t\t\t\twidth: 40rpx;\r\n\t\t\t\t\t\t\theight: 33rpx;\r\n\t\t\t\t\t\t\tmargin-right: 10rpx;\r\n\t\t\t\t\t\t}\r\n\r\n\r\n\t\t\t\t\t\t.text_1 {\r\n\t\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t\t\tcolor: #343434;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.card-box_title_content_item_bom {\r\n\t\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\t\tfont-size: 20rpx;\r\n\r\n\t\t\t\t\t\tspan:nth-child(1) {\r\n\t\t\t\t\t\t\tcolor: #00C2A0;\r\n\t\t\t\t\t\t\t// margin-right: 10rpx;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\tspan:nth-child(2) {\r\n\t\t\t\t\t\t\tcolor: #C3C3C3;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t}\r\n\r\n\t\t\t}\r\n\r\n\r\n\t\t}\r\n\r\n\t\t.card {\r\n\t\t\twidth: 690rpx;\r\n\t\t\tmargin: 30rpx auto;\r\n\t\t\tpadding: 23rpx 0;\r\n\t\t\tbox-sizing: border-box;\r\n\t\t\tbackground: #FFFFFF;\r\n\t\t\tborder-radius: 15rpx 15rpx 15rpx 15rpx;\r\n\r\n\t\t\t&:last-child {\r\n\t\t\t\tmargin-bottom: 50rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.card_title {\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\tcolor: #343434;\r\n\t\t\t\tpadding: 0 33rpx;\r\n\t\t\t\tbox-sizing: border-box;\r\n\t\t\t}\r\n\r\n\t\t\t.card_groud {\r\n\t\t\t\tpadding: 0 33rpx;\r\n\t\t\t\tbox-sizing: border-box;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tflex-wrap: nowrap;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: space-between;\r\n\r\n\t\t\t\t.card_groud_item {\r\n\t\t\t\t\t// width: 25%;\r\n\t\t\t\t\theight: 100rpx;\r\n\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\tmargin-top: 43rpx;\r\n\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\t\tcolor: #343434;\r\n\r\n\t\t\t\t\timage {\r\n\t\t\t\t\t\twidth: 49rpx;\r\n\t\t\t\t\t\theight: 39rpx;\r\n\t\t\t\t\t\tmargin-bottom: 10rpx;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.svgImg {\r\n\t\t\t\t\t\twidth: 60rpx;\r\n\t\t\t\t\t\theight: 50rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.border {\r\n\t\t\t\t\tmargin-top: 30rpx;\r\n\t\t\t\t\twidth: 38rpx;\r\n\t\t\t\t\theight: 110rpx;\r\n\t\t\t\t\t// border-radius: 10rpx;\r\n\t\t\t\t\t// background-color: #ecfaf8;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.lastflex {\r\n\t\t\t\tpadding: 0 33rpx;\r\n\t\t\t\tbox-sizing: border-box;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tflex-wrap: nowrap;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: flex-start;\r\n\r\n\t\t\t\t.card_groud_item {\r\n\t\t\t\t\t// width: 25%;\r\n\t\t\t\t\theight: 100rpx;\r\n\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\tmargin-top: 43rpx;\r\n\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\t\tcolor: #343434;\r\n\t\t\t\t\tmargin-right: 60rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./newme.vue?vue&type=style&index=0&id=70684b36&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./newme.vue?vue&type=style&index=0&id=70684b36&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754557259046\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}