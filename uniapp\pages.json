{
	"easycom": {
		"autoscan": true,
		"custom": {
			// 保留原有 uview-ui 规则
			"^u-(.*)": "uview-ui/components/u-$1/u-$1.vue",

			// 新增 uni-ui 规则（注意前缀区分）
			"^uni-(.*)": "@dcloudio/uni-ui/lib/uni-$1/uni-$1.vue"
		}
	},
	"pages": [ //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
		{
			"path": "pages/index/index",
			"style": {
				"navigationBarTitleText": "首页",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/order/order",

			"style": {
				"navigationBarTitleText": "校区服务",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		// {
		// 	"path": "pages/order_form/order_form",
		// 	"style": {
		// 		"navigationBarTitleText": "订单",
		// 		"enablePullDownRefresh": false,
		// 		"navigationStyle": "custom"
		// 	}
		// },
		{
			"path": "pages/me/me",
			"style": {
				"navigationBarTitleText": "我的",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		}

	],
	"subPackages": [{
			"root": "pages/me_all",
			"pages": [{
					"path": "my_orders/my_orders",
					"meta": {
						"auth": true, //需要登录
						"async": true, //是否同步
						"title": "我的", //标题
						"group": "我的订单" //分组
					},
					"style": {
						"navigationBarTitleText": "全部订单",
						"enablePullDownRefresh": false,
						"navigationBarBackgroundColor": "#00C2A0",
						"navigationBarTextStyle": "white"
					}
				},
				{
					"path": "all_orders/all_orders", //机构端
					"meta": {
						"auth": true, //需要登录
						"async": true, //是否同步
						"title": "我的", //标题
						"group": "我的订单" //分组
					},
					"style": {
						"navigationBarTitleText": "全部订单",
						"enablePullDownRefresh": false,
						"navigationBarBackgroundColor": "#00C2A0",
						"navigationBarTextStyle": "white"
					}
				},
				{
					"path": "order_off/order_off",
					"meta": {
						"auth": true, //需要登录
						"async": true, //是否同步
						"title": "我的", //标题
						"group": "我的订单" //分组
					},
					"style": {
						"navigationBarTitleText": "订单核销",
						"enablePullDownRefresh": false,
						"navigationBarBackgroundColor": "#00C2A0",
						"navigationBarTextStyle": "white"
					}
				},
				// 
				{
					"path": "coupon/coupon",
					"meta": {
						"auth": true, //需要登录
						"async": true, //是否同步
						"title": "我的", //标题
						"group": "我的优惠劵" //分组
					},
					"style": {
						"navigationBarTitleText": "我的优惠劵",
						"enablePullDownRefresh": false,
						"navigationBarBackgroundColor": "#fff"
					}
				},
				{
					"path": "coupon_collection/coupon_collection",
					"meta": {
						"auth": true, //需要登录
						"async": true, //是否同步
						"title": "我的", //标题
						"group": "领取优惠劵" //分组
					},
					"style": {
						"navigationBarTitleText": "领取优惠劵",
						"enablePullDownRefresh": false,
						"navigationBarBackgroundColor": "#00C2A0",
						"navigationBarTextStyle": "white"
					}
				}, {
					"path": "personage/personage",
					"meta": {
						"auth": true, //需要登录
						"async": true, //是否同步
						"title": "我的", //标题
						"group": "个人资料" //分组
					},
					"style": {
						"navigationBarTitleText": "个人资料",
						"enablePullDownRefresh": false,
						"navigationBarBackgroundColor": "#fff"
					}

				}, {
					"path": "orderdetails/orderdetails",
					"meta": {
						"auth": true, //需要登录
						"async": true, //是否同步
						"title": "我的", //标题
						"group": "订单详情" //分组
					},
					"style": {
						"navigationBarTitleText": "订单详情",
						"navigationBarBackgroundColor": "#fff",
						"enablePullDownRefresh": false
					}

				},

				{
					"path": "order_detail/order_detail",
					"meta": {
						"auth": true, //需要登录
						"async": true, //是否同步
						"title": "我的", //标题
						"group": "订单详情" //分组
					},
					"style": {
						"navigationBarTitleText": "订单详情",
						"navigationBarBackgroundColor": "#00C2A0",
						"navigationBarTextStyle": "white",
						"enablePullDownRefresh": false
					}

				},
				{
					"path": "codeqr/codeqr",
					"meta": {
						"auth": true, //需要登录
						"async": true, //是否同步
						"title": "我的", //标题
						"group": "会员码" //分组
					},
					"style": {
						"navigationBarTitleText": "会员码",
						"navigationStyle": "custom",
						"enablePullDownRefresh": false
					}

				}
			]
		},
		{
			"root": "pages/order_all",
			"pages": [
				// 
				{
					"path": "submit_order/submit_order",
					"meta": {
						"auth": true, //需要登录
						"async": true, //是否同步
						"title": "", //标题
						"group": "确认订单" //分组
					},
					"style": {
						"navigationBarTitleText": "确认订单",
						"navigationBarBackgroundColor": "#00C2A0",
						"navigationBarTextStyle": "white"
					}
				},
				{
					"path": "affirm_order/affirm_order",
					"meta": {
						"auth": true, //需要登录
						"async": true, //是否同步
						"title": "点餐", //标题
						"group": "确认订单" //分组
					},
					"style": {
						"navigationBarTitleText": "确认订单",
						"navigationBarBackgroundColor": "#fff"
					}
				}, {
					"path": "shipping_address/shipping_address",
					"meta": {
						"auth": true, //需要登录
						"async": true, //是否同步
						"title": "点餐", //标题
						"group": "收货地址" //分组
					},
					"style": {
						"navigationBarTitleText": "收货地址",
						"enablePullDownRefresh": false,
						"navigationBarBackgroundColor": "#fff"
					}

				},
				{
					"path": "add_address/add_address",
					"meta": {
						"auth": true, //需要登录
						"async": true, //是否同步
						"title": "点餐", //标题
						"group": "收货地址" //分组
					},
					"style": {
						"navigationBarTitleText": "添加地址",
						"enablePullDownRefresh": false,
						"navigationBarBackgroundColor": "#fff"
					}

				},
				{
					"path": "edit_address/edit_address",
					"meta": {
						"auth": true, //需要登录
						"async": true, //是否同步
						"title": "点餐", //标题
						"group": "收货地址" //分组
					},
					"style": {
						"navigationBarTitleText": "修改地址",
						"enablePullDownRefresh": false,
						"navigationBarBackgroundColor": "#fff"
					}

				},
				{
					"path": "selectstore/selectstore",
					"meta": {
						"auth": true, //需要登录
						"async": true, //是否同步
						"title": "点餐", //标题
						"group": "选择门店" //分组
					},
					"style": {
						"navigationBarTitleText": "选择校区",
						"navigationBarBackgroundColor": "#fff",
						"enablePullDownRefresh": false
					}

				}, {
					"path": "goodsordersearch/goodsordersearch",
					"meta": {
						"auth": true, //需要登录
						"async": true, //是否同步
						"title": "点餐", //标题
						"group": "商品详情" //分组
					},
					"style": {
						"navigationBarTitleText": "商品详情",
						"enablePullDownRefresh": false
					}

				}, {
					"path": "goodsordersearch/goodsordersearch2",
					"meta": {
						"auth": true, //需要登录
						"async": true //是否同步
					},
					"style": {
						"navigationBarTitleText": "预约详情",
						"enablePullDownRefresh": false
					}

				}
			]
		},
		{
			"root": "subpkg/report_all",
			"pages": [{
					"path": "index/index",
					"meta": {
						"auth": true, //需要登录
						"async": true, //是否同步
						"title": "报告", //标题
						"group": "入学报告" //分组
					},
					"style": {
						"navigationStyle": "custom"
					}
				}, {
					"path": "active/active",
					"meta": {
						"auth": true, //需要登录
						"async": true, //是否同步
						"title": "报告", //标题
						"group": "大学陪跑" //分组
					},
					"style": {
						"navigationStyle": "custom"
					}
				}, {
					"path": "report/report",
					"meta": {
						"auth": true, //需要登录
						"async": true, //是否同步
						"title": "报告", //标题
						"group": "大学陪跑" //分组
					},
					"style": {
						"navigationStyle": "custom"
					}
				}, {
					"path": "report_view/report_view",
					"meta": {
						"auth": true, //需要登录
						"async": true, //是否同步
						"title": "报告", //标题
						"group": "入学报告" //分组
					},
					"style": {
						"navigationStyle": "custom"
					}
				}, {
					"path": "plan/plan",
					"meta": {
						"auth": true, //需要登录
						"async": true, //是否同步
						"title": "报告", //标题
						"group": "入学报告" //分组
					},
					"style": {
						"navigationStyle": "custom"
					}
				}, {
					"path": "university_companion/university_companion",
					"meta": {
						"auth": true, //需要登录
						"async": true, //是否同步
						"title": "陪跑", //标题
						"group": "大学陪跑" //分组
					},
					"style": {
						"navigationStyle": "custom"
					}
				},
				{
					"path": "upload_content/upload_content",
					"meta": {
						"auth": true, //需要登录
						"async": true, //是否同步
						"title": "陪跑", //标题
						"group": "大学陪跑" //分组
					},
					"style": {
						"navigationStyle": "custom"
					}
				}

			]
		}

	],
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "uni-app",
		"navigationBarBackgroundColor": "#F8F8F8",
		"backgroundColor": "#F8F8F8"
	},
	"uniIdRouter": {},
	"tabBar": {
		"borderStyle": "white",
		"backgroundColor": "#FFFFFF",
		"color": "#777777",
		"selectedColor": "#00C2A0",
		"list": [{
				"pagePath": "pages/index/index",
				"iconPath": "./static/tab/page_2.png",
				"selectedIconPath": "./static/tab/page_1.png",
				"text": "首页"
			},
			{
				"pagePath": "pages/order/order",
				"iconPath": "./static/tab/oder_2.png",
				"selectedIconPath": "./static/tab/oder_1.png",
				"text": "校区服务"
			},
			// {
			// 	"pagePath": "pages/order_form/order_form",
			// 	"iconPath": "./static/tab/oder_form_2.png",
			// 	"selectedIconPath": "./static/tab/oder_form_1.png",
			// 	"text": "订单"
			// },
			{
				"pagePath": "pages/me/me",
				"iconPath": "./static/tab/my_2.png",
				"selectedIconPath": "./static/tab/my_1.png",
				"text": "我的"
			}
		]
	}
}